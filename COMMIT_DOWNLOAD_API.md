# Commit-Based Download API

This document describes the new API endpoint for downloading project code based on a specific commit ID from Azure DevOps repositories.

## Endpoint

```
GET /download/commit/{commit_id}
```

## Description

Downloads the project repository state as a ZIP file from a specific commit in the Azure DevOps repository.

## Parameters

### Path Parameters

- `commit_id` (string, required): The Git commit SHA hash (minimum 7 characters, full 40-character SHA recommended)

### Query Parameters

- `project_id` (string, required): The project ID to download
- `user_signature` (string, required): User signature for authentication

## Request Example

```bash
curl -X GET "http://localhost:8000/download/commit/abcdef1234567890abcdef1234567890abcdef12?project_id=your-project-id&user_signature=your-user-signature" \
  -H "Authorization: Bearer your-token" \
  -o project_commit_abcdef1.zip
```

## Response

### Success Response (200 OK)

- **Content-Type**: `application/zip`
- **Content-Disposition**: `attachment; filename=Mlo_{project_name}_commit_{commit_id}.zip`
- **Body**: ZIP file containing all repository files at the specified commit

### Error Responses

#### 400 Bad Request
- Invalid commit ID format
- Commit ID too short (less than 7 characters)
- Commit ID contains non-hexadecimal characters

```json
{
  "detail": "Invalid commit ID format"
}
```

#### 403 Forbidden
- User doesn't have access to the project

```json
{
  "detail": "Access denied"
}
```

#### 404 Not Found
- Project not found
- Repository not found
- Commit not found
- No files found at the specified commit

```json
{
  "detail": "Project not found"
}
```

#### 422 Unprocessable Entity
- Missing required parameters

```json
{
  "detail": [
    {
      "loc": ["query", "project_id"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

#### 500 Internal Server Error
- Azure DevOps API errors
- Repository access issues
- File processing errors

```json
{
  "detail": "Failed to fetch files"
}
```

## Implementation Details

### Validation
- Commit ID must be at least 7 characters long
- Commit ID must contain only hexadecimal characters (0-9, a-f, A-F)
- Project must exist and user must have access
- Commit must exist in the repository

### File Naming
The downloaded ZIP file follows this naming pattern:
```
Mlo_{sanitized_project_name}_commit_{first_7_chars_of_commit}.zip
```

Example: `Mlo_My_Project_commit_abcdef1.zip`

### Performance
- Uses concurrent file fetching for optimal performance
- Includes detailed timing logs for monitoring
- Handles large repositories efficiently

### Azure DevOps Integration
- Uses Azure DevOps Git API with GitVersionDescriptor
- Supports full commit SHA hashes
- Retrieves repository state exactly as it was at the specified commit

## Usage Examples

### Download Latest Commit
First, get the latest commit ID from your repository, then use it:

```bash
# Get latest commit (example using Azure DevOps REST API)
curl -X GET "https://dev.azure.com/{organization}/{project}/_apis/git/repositories/{repositoryId}/commits?$top=1" \
  -H "Authorization: Basic {base64_encoded_pat}"

# Use the commit ID from the response
curl -X GET "http://localhost:8000/download/commit/{commit_id}?project_id={project_id}&user_signature={user_signature}" \
  -o project_latest.zip
```

### Download Specific Historical Version
```bash
curl -X GET "http://localhost:8000/download/commit/1a2b3c4d5e6f7890abcdef1234567890abcdef12?project_id=my-project&user_signature=john.doe" \
  -o project_historical.zip
```

## Differences from Regular Download

| Feature | Regular Download (`/download/project/{project_id}`) | Commit Download (`/download/commit/{commit_id}`) |
|---------|---------------------------------------------------|------------------------------------------------|
| **Source** | Latest repository state | Specific commit state |
| **Parameters** | project_id, user_signature | commit_id, project_id, user_signature |
| **Filename** | `Mlo_{project_name}.zip` | `Mlo_{project_name}_commit_{commit_id}.zip` |
| **Use Case** | Current development version | Historical versions, releases, specific points in time |

## Error Handling

The endpoint includes comprehensive error handling:

1. **Input Validation**: Validates commit ID format before processing
2. **Authentication**: Ensures user has access to the project
3. **Repository Access**: Validates repository exists and is accessible
4. **Commit Validation**: Ensures commit exists in the repository
5. **File Processing**: Handles file fetch failures gracefully

## Logging

All operations are logged with detailed information:
- Request details (project ID, commit ID, user)
- Performance metrics (timing for each operation phase)
- Error details for troubleshooting
- Success confirmations with file counts and sizes

## Security

- Same authentication and authorization as existing download endpoints
- User access validation against project ownership
- Input sanitization for commit IDs
- No direct repository access without proper authentication
