[project]
name = "experience-studio-server"
version = "0.1.0"
description = "Api for Ascendion Experience Studio"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aiofiles==24.1.0",
    "asyncpg==0.30.0",
    "azure-devops>=7.1.0b4",
    "azure-identity>=1.21.0",
    "azure-storage-blob>=12.25.1",
    "beautifulsoup4==4.12.3",
    "black>=25.1.0",
    "bson>=0.5.10",
    "fastapi===0.115.11",
    "gitpython==3.1.44",
    "httpx==0.28.1",
    "identify>=2.6.12",
    "langchain==0.3.19",
    "matplotlib==3.10.1",
    "msal>=1.32.3",
    "msrest>=0.7.1",
    "opencv-python==*********",
    "chromadb==1.0.13",
    "datasets==2.14.5",
    "pandas==2.2.3",
    "pip-audit>=2.9.0",
    "pyjwt==2.10.1",
    "python-dotenv==1.0.1",
    "python-multipart>=0.0.20",
    "redis[hiredis]>=6.2.0",
    "requests==2.32.4",
    "ruff==0.9.9",
    "sse-starlette>=2.3.6",
    "tiktoken>=0.9.0",
    "uvicorn==0.34.0",
    "wcag-contrast-ratio==0.9",
]

[dependency-groups]
dev = [
    "pre-commit>=4.2.0",
    "pytest==8.3.5",
]
