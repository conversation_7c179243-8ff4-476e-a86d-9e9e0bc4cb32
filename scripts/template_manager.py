#!/usr/bin/env python3
"""Template Management Script for Azure Blob Storage"""

import os, sys, tempfile, zipfile, subprocess, logging
from datetime import datetime

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
from azure.storage.blob import BlobServiceClient
from settings.settings import settings

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

TEMPLATE_REPOSITORIES = {
    "react_tailwindcss": {
        "url": "https://dev.azure.com/ascendionava/experience-studio-preview-projects/_git/React_Tailwindcss_Template",
        "framework": "React", "design_library": "Tailwindcss"
    }
}
CONTAINER_NAME = "experience-studio-template-repos"
EXCLUDE_PATTERNS = {'.git', '.gitignore', '.DS_Store', '__pycache__', 'node_modules', '.env', '.venv', 'venv'}


class TemplateManager:
    def __init__(self):
        blob_url, sas_token = settings.azure.BLOB_STORAGE_URL, settings.azure.STORAGE_SAS_TOKEN.lstrip('?')
        if not sas_token: raise ValueError("SAS token is empty")
        
        account_url = blob_url.split('?')[0] if '?' in blob_url else blob_url
        if f'/{CONTAINER_NAME}' in account_url:
            account_url = account_url.replace(f'/{CONTAINER_NAME}', '')
        
        self.blob_client = BlobServiceClient(account_url=account_url, credential=sas_token)

    def process_templates(self):
        start_time = datetime.now()
        logger.info("Starting template update process")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            upload_count = 0
            for repo_key, config in TEMPLATE_REPOSITORIES.items():
                try:
                    # Clone
                    clone_path = os.path.join(temp_dir, f"{config['framework']}_{config['design_library']}_Template")
                    subprocess.run(["git", "clone", config["url"], clone_path], check=True, capture_output=True)
                    
                    # Zip
                    zip_path = os.path.join(temp_dir, f"{config['framework']}_{config['design_library']}_Template.zip")
                    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                        for root, dirs, files in os.walk(clone_path):
                            dirs[:] = [d for d in dirs if d not in EXCLUDE_PATTERNS]
                            for file in files:
                                if file not in EXCLUDE_PATTERNS:
                                    file_path = os.path.join(root, file)
                                    zipf.write(file_path, os.path.relpath(file_path, clone_path))
                    
                    # Upload
                    blob_client = self.blob_client.get_blob_client(container=CONTAINER_NAME, blob=os.path.basename(zip_path))
                    with open(zip_path, 'rb') as data:
                        blob_client.upload_blob(data, overwrite=True)
                    
                    upload_count += 1
                    logger.info(f"Processed {os.path.basename(zip_path)}")
                except Exception as e:
                    logger.error(f"Failed to process {repo_key}: {e}")
            
            logger.info(f"Completed in {datetime.now() - start_time} - {upload_count}/{len(TEMPLATE_REPOSITORIES)} templates")


def main():
    try:
        if not all([settings.azure.BLOB_STORAGE_URL, settings.azure.STORAGE_SAS_TOKEN]):
            raise ValueError("Azure storage environment variables not set")
        TemplateManager().process_templates()
        logger.info("Template Manager completed successfully")
    except Exception as e:
        logger.error(f"Template Manager failed: {e}")
        sys.exit(1)


if __name__ == "__main__": main()

