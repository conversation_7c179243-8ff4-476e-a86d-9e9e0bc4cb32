from typing import Dict
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from src.model.request_schemas import (
    WireframeGeneratorRequest,
    WireframeRegenerateRequest,
)
from src.services.wireframe_generation.wireframe_generator import (
    get_wireframe_designs,
    regenerate_wireframe_designs,
    get_wireframe_intro,
)
from src.utils.logger import AppLogger
from src.utils.decorators.avaplus_decorator import handle_exceptions_and_log

router = APIRouter()
logger = AppLogger(__name__).get_logger()


@handle_exceptions_and_log(logger)
@router.post("/generate")
async def generate_wireframe(payload: WireframeGeneratorRequest) -> JSONResponse:
    """
    Generate wireframe designs based on user prompt.

    Args:
        payload: WireframeGeneratorRequest containing the prompt

    Returns:
        JSONResponse: Generated wireframe data or error message
    """
    try:
        logger.info("Wireframe | Endpoint called")

        response = await get_wireframe_designs(payload)

        if response is None:
            logger.error("Wireframe | Service returned None")
            return JSONResponse(
                status_code=500,
                content={"error": "Unable to process the request currently!"},
            )

        logger.info("Wireframe | Request completed successfully")
        return JSONResponse(status_code=200, content=response)

    except Exception as e:
        logger.error(f"Wireframe | Endpoint error: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to generate wireframe: {str(e)}"},
        )


@handle_exceptions_and_log(logger)
@router.post("/regenerate")
async def regenerate_wireframe(payload: WireframeRegenerateRequest) -> JSONResponse:
    """
    Regenerate wireframe designs based on existing code files and user request.

    Args:
        payload: WireframeRegenerateRequest containing the code files and user request

    Returns:
        JSONResponse: Regenerated wireframe code files or error message
    """
    try:
        logger.info("Wireframe Regenerate | Endpoint called")
        logger.info(
            f"Wireframe Regenerate | User request: {payload.user_request[:100]}..."
        )
        logger.info(f"Wireframe Regenerate | Code files count: {len(payload.code)}")

        response: list[Dict[str, str]] = await regenerate_wireframe_designs(payload)

        if response is None:
            logger.error("Wireframe Regenerate | Service returned None")
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Unable to process the regeneration request currently!"
                },
            )

        logger.info("Wireframe Regenerate | Request completed successfully")
        return JSONResponse(status_code=200, content=response)

    except Exception as e:
        logger.error(f"Wireframe Regenerate | Endpoint error: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to regenerate wireframe: {str(e)}"},
        )


@handle_exceptions_and_log(logger)
@router.post("/intro")
async def intro_wireframe(payload: WireframeRegenerateRequest) -> str:
    """
    Generate intro message for wireframe designs based on user request.

    Args:
        payload: WireframeRegenerateRequest containing the optional code files and user request

    Returns:
        str: A intro message for user about the project or request
    """
    try:
        logger.info("Wireframe Intro | Endpoint called")

        response = await get_wireframe_intro(payload)

        if response is None:
            logger.error("Wireframe Intro | Service returned None")
            raise HTTPException(
                status_code=500, detail="Unable to process the request currently!"
            )

        logger.info("Wireframe Intro | Request completed successfully")
        return response

    except Exception as e:
        logger.error(f"Wireframe Intro | Endpoint error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to generate wireframe intro: {str(e)}"
        )
