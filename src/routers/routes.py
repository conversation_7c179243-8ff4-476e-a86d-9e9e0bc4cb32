import datetime
import io

from fastapi import APIRouter, File, HTTPException, UploadFile
from fastapi.params import Query
from fastapi.responses import StreamingResponse, JSONResponse

from src.model.request_schemas import (
    AppTypeEnum,
    ConversationAPIRequest,
    DARequest,
    SmartPromptRequest,
)
from src.services.blob_service import AzureBlobService
from src.services.code_generation.code_generation import CodeGeneration
from src.services.code_generation.strategies.da_services import DAServices
from src.services.database.db_operations import (
    get_or_create_user,
    get_project_by_user_email,
)
from src.services.generation.generation_factory import GenerationFactory
from src.settings.settings import Settings
from src.utils.debug_file_logger import DebugFileLogger
from src.utils.decorators.avaplus_decorator import handle_exceptions_and_log
from src.utils.logger import AppLogger

router = APIRouter()
blob_service = AzureBlobService()
provider = GenerationFactory.get_generation("avaplus")
setting = Settings()

# Setup for conversation API
strategy = DAServices(provider, setting.da_creds)
code_generator = CodeGeneration(strategy)
logger = AppLogger(__name__).get_logger()


@router.get("/health")
def health_check():
    return JSONResponse(content={"status": "ok"}, status_code=200)


@router.post("/enhance")
async def enhance_prompt(request: SmartPromptRequest):
    mode = (
        "MLO_SMART_PROMPT_UI"
        if request.type.capitalize() == AppTypeEnum.UI
        else "MLO_SMART_PROMPT_APP"
    )
    try:
        da_request = DARequest(
            mode=mode,
            promptOverride=False,
            useCaseIdentifier=mode + setting.da_creds.DA_USECASE_IDENTIFIER,
            userSignature=request.userSignature,
            image=request.image,
            prompt=request.prompt,
        ).model_dump()
        smart_prompt = await provider.generate(da_request)
        # print("smart_prompt: ", smart_prompt)
        return JSONResponse(status_code=200, content=smart_prompt)
    except Exception as e:
        print("Error:", e)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/upload-image/")
async def upload_image(file: UploadFile = File(...)):
    filename = file.filename
    file_content = await file.read()
    result = blob_service.upload_image(file_content, filename)
    return result


@router.get("/download-image/{filename}")
async def download_image(filename: str):
    try:
        image_data = blob_service.download_image(filename)
        return StreamingResponse(io.BytesIO(image_data), media_type="image/jpeg")
    except HTTPException as e:
        return {"detail": e.detail}


@router.get("/project")
async def get_user_projects(
    user_signature: str = Query(...), num_projects: int = Query(...)
):
    try:
        # Assuming you have a function to get user_id from user_signature
        projects = await get_project_by_user_email(user_signature)
        # projects = await get_projects_by_user(user_id)

        # Sort projects by last_modified date in descending order
        sorted_projects = sorted(
            projects, key=lambda x: x["last_modified"], reverse=True
        )

        # Limit the number of projects returned
        limited_projects = sorted_projects[:num_projects]

        return {"status_code": 200, "projects": limited_projects}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/user")
async def get_user_id(email: str = Query(...)):
    user_id = await get_or_create_user(email)
    return JSONResponse(status_code=200, content=str(user_id))


@handle_exceptions_and_log(logger)
@router.post("/conversation")
async def process_conversation_api(
    request: ConversationAPIRequest,
    user_signature: str = Query(...),
) -> JSONResponse:
    logger.info(f"Conversation API request received from {user_signature}")

    try:
        # Convert the request to a dictionary for the service
        request_dict = request.model_dump()

        # Log the request for debugging
        logger.debug(f"Conversation request: {request_dict}")
        DebugFileLogger.log_to_file(
            f"api_conversation_request_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            request_dict,
        )

        # Process the conversation using the DA service
        response = await code_generator.process_conversation(request_dict)

        # Log the response for debugging
        logger.debug("Conversation response received")
        DebugFileLogger.log_to_file(
            f"api_conversation_response_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            response,
        )

        logger.info("Conversation API request processed successfully")
        return JSONResponse(status_code=200, content=response)

    except Exception as e:
        logger.error(f"Error processing conversation request: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to process conversation: {str(e)}"
        )
