from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Depends
from fastapi.responses import StreamingResponse
from fastapi.params import Query
import io
import asyncio

from src.dependencies import get_current_user
from src.services.azure.repo_downloader import create_project_zip, create_project_zip_from_commit_fast
from src.services.database.db_operations import get_project, get_project_by_user_email
router = APIRouter()


@router.get("/project/{project_id}")
async def download_project_code(
    project_id: str,
    user: int = Depends(get_current_user),
    user_signature: str = Query(...),
) -> StreamingResponse:
    """Download project code as zip - FAST VERSION"""
    try:
        zip_data = await create_project_zip(project_id, user_signature)
        filename = f"project_{project_id[:8]}.zip"
        return StreamingResponse(
            iter([zip_data]),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/commit/{commit_id}")
async def download_project_code_by_commit(
    commit_id: str,
    project_id: str = Query(...),
    user: int = Depends(get_current_user),
    user_signature: str = Query(...),
) -> StreamingResponse:
    """Download project code as zip from a specific commit - BLAZING FAST"""
    # Ultra-minimal validation
    if len(commit_id) < 7:
        raise HTTPException(400, "Invalid commit ID")

    zip_data = await create_project_zip_from_commit_fast(project_id, commit_id, user_signature)
    filename = f"project_{project_id[:8]}_commit_{commit_id[:7]}.zip"

    return StreamingResponse(
        iter([zip_data]),
        media_type="application/zip",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


@router.get("/debug/projects")
async def debug_list_projects(
    user_signature: str = Query(...),
) -> dict:
    """Debug endpoint to list all projects for a user"""
    try:
        projects = await get_project_by_user_email(user_signature)
        return {
            "user_email": user_signature,
            "projects_found": len(projects),
            "projects": projects
        }
    except Exception as e:
        return {"error": str(e)}


@router.get("/debug/project/{project_id}")
async def debug_get_project(
    project_id: str,
) -> dict:
    """Debug endpoint to check if a specific project exists"""
    try:
        project = await get_project(project_id)
        return {
            "project_id": project_id,
            "exists": project is not None,
            "project": dict(project) if project else None
        }
    except Exception as e:
        return {"error": str(e)}


@router.get("/debug/repo-name/{project_id}")
async def debug_repo_name(
    project_id: str,
    user_signature: str = Query(...),
) -> dict:
    """Debug endpoint to check what repository name would be generated"""
    try:
        from src.services.database.db_operations import get_project_and_user_fast

        project, user_id = await get_project_and_user_fast(project_id, user_signature)

        if not project:
            return {"error": "Project not found"}

        # Generate the same repo name as the download function
        repo_name = f"{project['project_name'].lower().replace(' ', '-')}-{str(user_id)[-4:]}-{project_id[-4:]}"

        return {
            "project_id": project_id,
            "project_name": project["project_name"],
            "user_id": str(user_id),
            "generated_repo_name": repo_name,
            "expected_repo_name": "insidebox-login-portal-d6a4-a3d4",
            "match": repo_name == "insidebox-login-portal-d6a4-a3d4"
        }
    except Exception as e:
        return {"error": str(e)}


@router.get("/debug/commits/{project_id}")
async def debug_commits(
    project_id: str,
    user_signature: str = Query(...),
) -> dict:
    """Debug endpoint to list recent commits for a project"""
    try:
        from src.services.database.db_operations import get_project_and_user_fast
        from src.services.azure.repo_downloader import _connector

        project, user_id = await get_project_and_user_fast(project_id, user_signature)

        if not project:
            return {"error": "Project not found"}

        # Generate repo name
        repo_name = f"{project['project_name'].lower().replace(' ', '-')}-{str(user_id)[-4:]}-{project_id[-4:]}"

        # Get repo
        repo = await _connector.create_or_get_azure_repo(repo_name)
        if not repo:
            return {"error": "Repository not found"}

        # Get recent commits
        ado_project_id_guid = await _connector._ensure_ado_project_guid()
        commits = await asyncio.to_thread(
            _connector._git_client.get_commits,
            repository_id=repo.id,
            project=ado_project_id_guid,
            search_criteria={'$top': 10}
        )

        commit_list = []
        for commit in commits:
            commit_list.append({
                "commit_id": commit.commit_id,
                "short_id": commit.commit_id[:7],
                "comment": commit.comment,
                "author": commit.author.name if commit.author else "Unknown",
                "date": str(commit.author.date) if commit.author else "Unknown"
            })

        return {
            "project_id": project_id,
            "repo_name": repo_name,
            "repo_id": repo.id,
            "commits": commit_list
        }
    except Exception as e:
        return {"error": str(e)}


@router.get("/debug/test-latest/{project_id}")
async def debug_test_latest(
    project_id: str,
    user_signature: str = Query(...),
) -> dict:
    """Debug endpoint to test downloading from latest commit"""
    try:
        from src.services.database.db_operations import get_project_and_user_fast
        from src.services.azure.repo_downloader import _connector

        project, user_id = await get_project_and_user_fast(project_id, user_signature)

        if not project:
            return {"error": "Project not found"}

        # Generate repo name
        repo_name = f"{project['project_name'].lower().replace(' ', '-')}-{str(user_id)[-4:]}-{project_id[-4:]}"

        # Get repo
        repo = await _connector.create_or_get_azure_repo(repo_name)
        if not repo:
            return {"error": "Repository not found"}

        # Try to get items from latest commit (no version descriptor)
        ado_project_id_guid = await _connector._ensure_ado_project_guid()

        try:
            items = await asyncio.to_thread(
                _connector._git_client.get_items,
                repository_id=repo.id,
                project=ado_project_id_guid,
                scope_path="/",
                recursion_level="full",
                include_content_metadata=False,
            )

            files = [item for item in items if item.git_object_type == "blob"]

            return {
                "success": True,
                "repo_name": repo_name,
                "repo_id": repo.id,
                "total_items": len(items),
                "files_count": len(files),
                "sample_files": [item.path for item in files[:10]]
            }
        except Exception as e:
            return {
                "error": f"Failed to get items from latest: {str(e)}",
                "repo_name": repo_name,
                "repo_id": repo.id
            }
    except Exception as e:
        return {"error": str(e)}


# @router.get("/list-templates")
# async def list_templates() -> dict:
#     """List all available templates in the blob storage for debugging"""
#     logger.info("Request to list all available templates")
#     try:
#         # Initialize Azure Blob Storage client
#         blob_service_client = BlobServiceClient(
#             account_url=settings.azure.AZURE_TEMPLATE_STORAGE_URL.split("?")[0],
#             credential=settings.azure.AZURE_TEMPLATE_STORAGE_TOKEN,
#         )

#         # Get container name from the URL
#         container_name = settings.azure.AZURE_TEMPLATE_STORAGE_URL.split("/")[-1].split(
#             "?"
#         )[0]
#         logger.info(f"Container name extracted: {container_name}")
#         logger.info(f"Token: {settings.azure.AZURE_TEMPLATE_STORAGE_TOKEN}")

#         # Get container client
#         container_client = blob_service_client.get_container_client(container_name)

#         # List all blobs in the container
#         blobs = []
#         try:
#             blob_list = container_client.list_blobs()
#             for blob in blob_list:
#                 blobs.append(
#                     {
#                         "name": blob.name,
#                         "size": blob.size,
#                         "last_modified": str(blob.last_modified),
#                     }
#                 )

#             # Connection details (sanitized for security)
#             connection_details = {
#                 "account_url": settings.azure.AZURE_TEMPLATE_STORAGE_URL.split("?")[0],
#                 "container_name": container_name,
#                 "blob_count": len(blobs),
#             }

#             return {"connection_details": connection_details, "blobs": blobs}
#         except Exception as e:
#             logger.error(f"Error listing blobs: {str(e)}")
#             return {
#                 "error": str(e),
#                 "connection_details": {
#                     "account_url": settings.azure.AZURE_TEMPLATE_STORAGE_URL.split("?")[
#                         0
#                     ],
#                     "container_name": container_name,
#                 },
#             }

#     except Exception as e:
#         logger.error(f"Failed to list templates: {str(e)}")
#         return {"error": str(e)}


@router.get("/template")
async def download_template(
    framework: str = Query(
        ..., description="Framework name (e.g., 'react', 'angular','vue')"
    ),
    design_library: str = Query(
        ...,
        description="Design library name (e.g., 'tailwindcss', 'materialui', 'bootstrap')",
    ),
) -> StreamingResponse:
    """Download template code as zip directly"""
    logger.info(f"Template download request for {framework}_{design_library}")
    try:
        # Validate required parameters
        if not framework or not design_library:
            logger.error("Missing required parameters: framework and design_library")
            raise HTTPException(status_code=400, detail="Missing required parameters")

        # Validate supported framework - currently only 'react'
        if framework.lower() != "react":
            logger.error(f"Unsupported framework: {framework}")
            raise HTTPException(status_code=400, detail="Unsupported framework")

        # Validate supported design library - currently only 'tailwindcss'
        if design_library.lower() != "tailwindcss":
            logger.error(f"Unsupported design library: {design_library}")
            raise HTTPException(status_code=400, detail="Unsupported design library")

        # Construct template filename with capitalized first letters
        framework_capitalized = framework.capitalize()
        design_library_capitalized = design_library.capitalize()
        template_filename = (
            f"{framework_capitalized}_{design_library_capitalized}_Template.zip"
        )

        # Log the template filename for debugging
        logger.info(f"Looking for template file: {template_filename}")

        # Initialize Azure Blob Storage client using template-specific settings
        storage_url = settings.azure.AZURE_TEMPLATE_STORAGE_URL

        # Extract account URL (remove SAS token if present)
        if "?" in storage_url:
            account_url = storage_url.split("?")[0]
        else:
            account_url = storage_url

        logger.info(f"Using template storage account URL: {account_url}")

        # Extract container name - use a fixed value if extraction fails
        try:
            # Try to extract from URL path
            from urllib.parse import urlparse

            parsed_url = urlparse(account_url)
            path_parts = parsed_url.path.strip("/").split("/")

            if len(path_parts) > 0:
                container_name = path_parts[-1]  # Last part of the path
            else:
                # Fallback to a known container name from template_manager.py
                container_name = "experience-studio-template-repos"

            logger.info(f"Using template container name: {container_name}")
        except Exception as e:
            logger.error(f"Error extracting container name: {str(e)}")
            # Fallback to a known container name from template_manager.py
            container_name = "experience-studio-template-repos"
            logger.info(f"Using fallback container name: {container_name}")

        # Clean up the SAS token if it starts with a question mark
        sas_token = settings.azure.AZURE_TEMPLATE_STORAGE_TOKEN
        if sas_token.startswith("?"):
            sas_token = sas_token[1:]

        # Get the account name from the URL
        account_name = account_url.split("//")[1].split(".")[0]

        # Construct a proper account URL without container path
        proper_account_url = f"https://{account_name}.blob.core.windows.net"
        logger.info(f"Constructed proper account URL: {proper_account_url}")

        # Initialize the blob service client with the proper account URL
        blob_service_client = BlobServiceClient(
            account_url=proper_account_url, credential=sas_token
        )

        # Get container client
        container_client = blob_service_client.get_container_client(container_name)

        # List blobs to check if the template exists
        # blob_exists = False
        try:
            blob_list = list(
                container_client.list_blobs(name_starts_with=template_filename)
            )
            if blob_list:
                # blob_exists = True
                logger.info(f"Template file found in container: {template_filename}")
            else:
                # List all blobs to see what's available
                all_blobs = list(container_client.list_blobs())
                blob_names = [blob.name for blob in all_blobs]
                logger.info(f"Template file not found. Available blobs: {blob_names}")
        except Exception as e:
            logger.error(f"Error checking if blob exists: {str(e)}")
            raise HTTPException(
                status_code=500, detail=f"Error accessing blob storage: {str(e)}"
            )

        # Download the template zip file
        try:
            blob_client = container_client.get_blob_client(template_filename)
            logger.info(f"Attempting to download blob: {template_filename}")

            download_stream = blob_client.download_blob()
            template_data = download_stream.readall()

            logger.info(
                f"Template downloaded successfully: {template_filename}, size: {len(template_data)} bytes"
            )

            # Return the zip file as a streaming response
            return StreamingResponse(
                io.BytesIO(template_data),
                media_type="application/zip",
                headers={
                    "Content-Disposition": f"attachment; filename={template_filename}"
                },
            )
        except Exception as e:
            logger.error(f"Failed to download template from blob storage: {str(e)}")
            raise HTTPException(
                status_code=404,
                detail=f"Template not found: {template_filename}. Error: {str(e)}",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to download template for {framework}_{design_library}: {str(e)}"
        )
        raise HTTPException(status_code=500, detail=str(e))


# Keep the original get_template_url endpoint for backward compatibility
# @router.get("/get-template-url")
# async def get_template_url(
#     framework: str = Query(
#         ..., description="Framework name (e.g., 'react', 'angular','vue')"
#     ),
#     design_library: str = Query(
#         ...,
#         description="Design library name (e.g., 'tailwindcss', 'materialui', 'bootstrap')",
#     ),
# ) -> dict:
#     """Get template download URL."""
#     logger.info(f"Template URL request for {framework}_{design_library}")
#     try:
#         # Validate required parameters
#         if not framework or not design_library:
#             logger.error("Missing required parameters: framework and design_library")
#             return {"download_url": None}

#         # Validate supported framework - currently only 'react'
#         if framework.lower() != "react":
#             logger.error(f"Unsupported framework: {framework}")
#             return {"download_url": None}

#         # Validate supported design library - currently only 'tailwindcss'
#         if design_library.lower() != "tailwindcss":
#             logger.error(f"Unsupported design library: {design_library}")
#             return {"download_url": None}

#         # Construct template filename with capitalized first letters
#         framework_capitalized = framework.capitalize()
#         design_library_capitalized = design_library.capitalize()
#         template_filename = (
#             f"{framework_capitalized}_{design_library_capitalized}_Template.zip"
#         )

#         # Build download URL using Azure settings
#         base_url = settings.azure.BLOB_STORAGE_URL

#         # Properly construct the Azure Blob Storage URL
#         # The AZURE_STORAGE_SAS_URL should be like: https://account.blob.core.windows.net/container?sas_token
#         # We need to insert the blob name before the query parameters
#         if "?" in base_url:
#             # Split the URL and SAS token
#             url_parts = base_url.split("?", 1)
#             blob_base_url = url_parts[0].rstrip("/")
#             sas_token = url_parts[1]

#             # Construct the final URL: base_url/filename?sas_token
#             download_url = f"{blob_base_url}/{template_filename}?{sas_token}"
#         else:
#             # Fallback: if no SAS in URL, try to use separate token
#             sas_token = settings.azure.STORAGE_SAS_TOKEN
#             blob_base_url = base_url.rstrip("/")

#             if sas_token:
#                 # Clean up SAS token format
#                 if sas_token.startswith("?"):
#                     sas_token = sas_token[1:]
#                 download_url = f"{blob_base_url}/{template_filename}?{sas_token}"
#             else:
#                 download_url = f"{blob_base_url}/{template_filename}"

#         logger.info(
#             f"Template URL generated for {framework}_{design_library}: {template_filename}"
#         )
#         logger.debug(f"Download URL constructed: {download_url}")

#         return {"download_url": download_url}

#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(
#             f"Failed to generate template URL for {framework}_{design_library}: {str(e)}"
#         )
#         raise HTTPException(status_code=500, detail=str(e))
