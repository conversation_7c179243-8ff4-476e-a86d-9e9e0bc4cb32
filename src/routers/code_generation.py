import asyncio
import datetime
import json
import uuid

import aiofiles
from fastapi import APIRouter, BackgroundTasks, HTTPException, Query, Request
from fastapi.params import Depends
from fastapi.responses import JSONResponse


from src.dependencies import get_current_user
from src.exceptions.exception import DBConnectionError
from src.model.base_schema import ProjectStatusV2
from src.model.request_schemas import (
    AppProgressEnum,
    CodeGenerationRequest,
    CodeRegenerationRequest,
    ProjectCreateRequest,
    ProjectStatusEnum,
)
from src.model.response_schemas import CodeResponse, JobStateResponse
from src.services.code_generation.code_generation import CodeGeneration
from src.services.code_generation.strategies.da_services import DAServices
from src.services.database.db_operations import (
    create_project,
    create_project_status_v2,
    get_or_create_user,
    get_project_details,
    get_project_status_v2,
    update_project_name,
    update_project_status_v2,
)
from src.services.generation.generation_factory import GenerationFactory

# Azure services imports
from src.services.azure.ado_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.services.React_prompt_helper import <PERSON><PERSON><PERSON>rom<PERSON><PERSON><PERSON><PERSON>
from src.core.sse_pubsub import RedisStreamPublisher
from src.settings.settings import Settings
from src.utils.debug_file_logger import DebugFileLogger
from src.utils.decorators.avaplus_decorator import handle_exceptions_and_log
from src.utils.design_system_parser import convert_tailwind_to_design_tokens
from src.utils.logger import AppLogger
from src.utils.code_regeneration.post_processor import (
    merge_code_lists,
    extract_code_from_api_response,
)

# from starlette.background import BackgroundTask


STREAMING_QUEUES = {}

router = APIRouter()

settings = Settings()

provider = GenerationFactory.get_generation("avaplus")
strategy = DAServices(provider, settings.da_creds)
code_generator = CodeGeneration(strategy)
logger = AppLogger(__name__).get_logger()

# Global Redis connection pool (only created if SSE_BACKEND is 'redis')
redis_pool = None


def safe_json_parse(json_string: str, field_name: str = "code") -> list:
    """
    Safely parse JSON string with proper error handling and cleaning.

    Args:
        json_string: The JSON string to parse
        field_name: The field to extract from the parsed JSON

    Returns:
        List from the specified field, or empty list if parsing fails
    """
    try:
        # First attempt: try parsing as-is
        data = json.loads(json_string)
        result = data.get(field_name, [])

        if not isinstance(result, list):
            logger.warning(
                f"✗ Field '{field_name}' is not a list, converting to empty list"
            )
            return []

        return result

    except json.JSONDecodeError as e:
        logger.warning(f"✗ Initial JSON parse failed: {str(e)}")

        try:
            # Second attempt: clean control characters and try again
            cleaned = (
                json_string.replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t")
            )
            data = json.loads(cleaned)
            result = data.get(field_name, [])

            if not isinstance(result, list):
                logger.warning(f"✗ Field '{field_name}' is not a list after cleaning")
                return []

            logger.info("✓ JSON parsed successfully after cleaning control characters")
            return result

        except json.JSONDecodeError as e2:
            logger.error(f"✗ Failed to parse JSON even after cleaning: {str(e2)}")
            logger.debug(
                f"✗ Problematic JSON around position {e2.pos}: {json_string[max(0, e2.pos-50):e2.pos+50]}"
            )
            return []


@handle_exceptions_and_log(logger)
@router.post("/generate/app")
async def code_generation(
    request: CodeGenerationRequest,
    background_tasks: BackgroundTasks,
    request_obj: Request,
    user: int = Depends(get_current_user),
    user_signature: str = Query(...),
) -> JSONResponse:
    """Generate a complete React application from a UI design image."""

    start_time = datetime.datetime.now()
    project_data = ProjectCreateRequest(
        project_name=request.project_name,
        project_description=request.project_description,
        project_type=request.project_type,
        project_state=request.project_state,
    )

    status = await create_new_project_core(project_data, user, user_signature)
    if status.status_code != 200:
        logger.error("Unable to create project in DB.")
        raise DBConnectionError()

    project_id = status.code
    request_id = str(uuid.uuid4())
    logger.debug(f"Request ID: {request_id}")

    logger.info("START | App Generation | Code generation process initiated")
    logger.debug(
        f"Input: Framework={request.framework}, Design Library={request.design_library}"
    )

    request_dict = request.model_dump()
    request_dict.update(
        {"userSignature": user_signature, "user_input": request.project_description}
    )

    redis_publisher = request_obj.app.state.redis_stream_publisher

    background_tasks.add_task(
        process_code_generation,
        request_dict,
        project_id,
        user,
        user_signature,
        request_id,
        start_time,
        redis_publisher,  # Pass the publisher instance
    )

    return JSONResponse(
        status_code=202, content={"job_id": request_id, "project_id": project_id}
    )


@handle_exceptions_and_log(logger)
async def process_code_generation(
    request,
    project_id,
    user,
    user_signature,
    request_id,
    start_time,
    redis_stream_publisher: RedisStreamPublisher,
):
    state = {}
    states = []
    try:
        state = {
            "status": ProjectStatusEnum.PENDING,
            "progress": AppProgressEnum.OVERVIEW,
            "log": "Senior Code Architect | Initializing your project workspace",
            "progress_description": f"Hey there! I'm absolutely thrilled to work on '{request['project_name']}' with you! Let me dive into your vision and start crafting something amazing. I can already tell this is going to be an exciting project!",
            "event_type": "initial-code-gen",
            "metadata": [],
        }

        states = prepare_states(states, state)
        await create_project_status_v2(
            project_id,
            request_id,
            states[0]["status"],
            states[0]["log"],
            states[0]["progress"],
            states[0]["progress_description"],
            json.dumps(states),
            json.dumps(states[0]["metadata"]),
        )
        await redis_stream_publisher.publish_project_update(request_id, state)

        # Canny Edge Processing and appending to request
        # image_uris = request["image"]
        # request["image"] = process_edges_image(image_uris)

        project_brief = await code_generator.generate_project_brief(request)
        await update_project_name(
            project_id, project_brief["name"], project_brief["description"]
        )

        state = {
            "status": ProjectStatusEnum.IN_PROGRESS,
            "progress": AppProgressEnum.OVERVIEW,
            "log": "Lead Project Strategist | Crafting your comprehensive project blueprint",
            "progress_description": f"Wow! I love what you're building! Here's the exciting roadmap I've mapped out for '{project_brief['name']}':\n\n✨ {project_brief['overview']}\n\nThis is going to be incredible! I'm already envisioning how smooth and intuitive your users' experience will be. Ready to bring this vision to life with some seriously clean, scalable code!",
            "event_type": "initial-code-gen",
            "metadata": [
                {
                    "type": "artifact",
                    "data": {"type": "text", "data": project_brief["overview"]},
                },
                {
                    "type": "ref_code",
                    "data": f"{project_brief['name']}",
                },
            ],
        }

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        logger.info("-- Task: Starting code generation process")

        async def log_picker_callback(log_collector):
            nonlocal states
            state = {
                "status": log_collector["project_status"],
                "progress": log_collector["app_progress"],
                "log": log_collector["log"],
                "progress_description": log_collector["progress_description"],
                "event_type": "initial-code-gen",
                "metadata": log_collector["metadata"],
            }
            states = await publish_and_get_state(
                redis_stream_publisher, request_id, state, states
            )

        # 1. Generate PRD
        logger.info("PRD | Generating product requirements")
        detailed_prd = await code_generator.generate_prd(request)
        DebugFileLogger.log_to_file(f"prd_{request_id}.json", detailed_prd)
        logger.debug(f"PRD | Complete | Time: {datetime.datetime.now() - start_time}")

        state = states.pop()
        state["status"] = ProjectStatusEnum.COMPLETED
        state["metadata"] = [
            {
                "type": "artifact",
                "data": {"type": "text", "data": json.dumps(detailed_prd)},
            },
        ]
        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        state = {
            "status": ProjectStatusEnum.IN_PROGRESS,
            "progress": AppProgressEnum.SEED_PROJECT_INITIALIZED,
            "log": f"Cloud Infrastructure Specialist | Provisioning your {request['framework']} environment",
            "progress_description": f"This is where the magic happens! I'm setting up your premium {request['framework']} development environment with all the bells and whistles:\n\n **Tech Stack**: {request['framework']} powered by {request['design_library']}\n **Infrastructure**: Azure DevOps with automated workflows (because who has time for manual deployments?)\n **Design System**: Beautiful {request['design_library']} components ready to wow your users\n **Developer Tools**: Pre-configured build pipeline that just works\n\nI'm following all the industry best practices and modern standards - your future self will thank you for this solid foundation!",
            "event_type": "initial-code-gen",
            "metadata": [],
        }

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        # gh_repo_connector = GithubRepoConnector()
        # repo_name = f"mlo-{str(user)[-4:]}-{project_id[-4:]}"
        # gh_repo_data = await gh_repo_connector.create_repo_from_template(
        #     settings.github_config.GH_ORG,
        #     f"{request['framework'].capitalize()}_{request['design_library'].capitalize()}_Template",
        #     repo_name,
        #     settings.github_config.GH_ORG,
        # )
        # netlify_connector = NetlifyConnector(settings.netlify_config)

        # gh_full_name = gh_repo_data["full_name"]
        # gh_repo_id = gh_repo_data["id"]
        # gh_default_branch = gh_repo_data["default_branch"]
        # gh_is_private = gh_repo_data["private"]
        # gh_owner_login = gh_repo_data["owner"]["login"]

        # netlify_site_data = None

        # netlify_site_data = await netlify_connector.create_site_and_link_repo(
        #     github_repo_full_name=gh_full_name,
        #     github_repo_id=gh_repo_id,
        #     github_default_branch=gh_default_branch,
        #     is_private_github_repo=gh_is_private,
        #     build_command="npm run build",
        #     publish_directory="dist",
        #     env_vars={"NODE_VERSION": "22"},
        #     custom_site_name=repo_name,
        #     github_repo_owner_for_installation_lookup=gh_owner_login,
        #     installation_id=settings.github_config.GH_INSTALLATION_ID,
        # )

        # live_url = netlify_site_data.get("ssl_url") or netlify_site_data.get("url")

        repo_name = f"{project_brief['name'].lower().replace(' ', '-')}-{str(user)[-4:]}-{project_id[-4:]}"
        # Initialize Azure DevOps helper
        ado_helper = AdoHelper(repo_name, "azure-pipelines.yaml")

        # Create Azure DevOps repository from template (seed project)
        commit_sha = await ado_helper.create_repository_and_deploy(
            repo_name,
            True,
            f"{request['framework'].capitalize()}_{request['design_library'].capitalize()}_Template",
            swa_app_name="sample-swa-test-2",
            # swa_app_name=f"ExperienceStudio-{str(user)[-4:]}",
        )

        logger.info(f"Seed project created - Repo: {repo_name}, Commit: {commit_sha}")

        # Note: Azure pipeline will automatically create Static Web App with random URL
        # URL will be available in Azure portal after deployment completes

        state = states.pop()
        state["status"] = ProjectStatusEnum.COMPLETED
        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        state = {
            "status": ProjectStatusEnum.IN_PROGRESS,
            "progress": AppProgressEnum.FILE_QUEUE,
            "log": f"Senior Component Architect | Mapping your {request['framework']} ecosystem",
            "progress_description": f"Time for some architectural wizardry! I'm carefully analyzing your requirements to design the perfect {request['framework']} component structure. Think of this as creating the blueprint for your digital masterpiece - every component, every file, strategically planned to create something that's not just functional, but absolutely beautiful to work with. Your codebase is going to be so clean and organized, other developers will be jealous!",
            "event_type": "initial-code-gen",
            "metadata": [],
        }

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        # 2. Identify files
        identified_files = await code_generator.identify_files(request, detailed_prd)
        DebugFileLogger.log_to_file(
            f"identified_files_{request_id}.json", identified_files
        )
        logger.debug(
            f"Files | Identified | Time: {datetime.datetime.now() - start_time}"
        )

        async with aiofiles.open("reactFiles.json", "r") as f:
            react_files = json.loads(await f.read())

        # 3.1. Extract design system files
        design_system_files = {
            "src/index.css": react_files.get("src/index.css", ""),
            "tailwind.config.ts": react_files.get("tailwind.config.ts", ""),
        }

        state = states.pop()
        state["status"] = ProjectStatusEnum.COMPLETED
        state["metadata"] = [
            {"type": "file_names", "data": identified_files.get("filesToGenerate")}
        ]

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        state = {
            "status": ProjectStatusEnum.IN_PROGRESS,
            "progress": AppProgressEnum.DESIGN_SYSTEM_MAPPED,
            "log": f"Lead Design System Engineer | Crafting your {request['design_library']} design foundation",
            "progress_description": f"Now we're getting to the fun part! I'm creating a stunning, cohesive design system using {request['design_library']} that'll make your app absolutely gorgeous. Every color, every font choice, every little detail is being crafted with your users in mind. I'm talking pixel-perfect components that not only look amazing but feel intuitive to use. Your app is going to have that premium, polished feel that users absolutely love!",
            "event_type": "initial-code-gen",
            "metadata": [],
        }

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        # 3.2. Generate design system
        design_system_code = await code_generator.generate_design_system(
            detailed_prd, request, design_system_files
        )
        logger.info("Design | System generated ✓")
        DebugFileLogger.log_to_file(
            f"design_system_{request_id}.json", design_system_code
        )
        logger.debug(
            f"Design | Complete | Time: {datetime.datetime.now() - start_time}"
        )
        state = states.pop()
        state["status"] = ProjectStatusEnum.COMPLETED
        state["progress_description"] = (
            f"Your design system is ready to rock! Here's what I've cooked up for you:\n\n **Color Magic**: {detailed_prd['designSystem']['colorPalette']['notes']}\n\n **Typography That Speaks**: {detailed_prd['designSystem']['typography']['notes']}\n\n **Perfect Spacing**: {detailed_prd['designSystem']['spacing']['notes']}\n\nThis isn't just a design system - it's the visual DNA of your app! Every element will work together harmoniously to create an experience your users will absolutely love."
        )

        # Extract design tokens from the generated design system
        try:
            if (
                isinstance(design_system_code, dict)
                and "src/index.css" in design_system_code
                and "tailwind.config.ts" in design_system_code
            ):
                logger.info("Design Tokens | Extracting design tokens")
                design_tokens = convert_tailwind_to_design_tokens(design_system_code)
                color_count = len(
                    design_tokens.get("design_tokens", {}).get("colors", [])
                )
                logger.info(f"Design Tokens | Extracted {color_count} colors ✓")
                DebugFileLogger.log_to_file(
                    f"design_tokens_{request_id}.json", design_tokens
                )
            else:
                logger.info("Design Tokens | Using original design system")
                design_tokens = design_system_code
        except Exception as e:
            logger.error(f"Design Tokens | Token extraction failed: {e}")
            design_tokens = design_system_code

        state["metadata"] = [
            {
                "type": "artifact",
                "data": {
                    "data": design_tokens,
                    "type": "json",
                },
            }
        ]

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        # 4. Categorize files
        categorized_files = ReactPromptHelper._categorize_files(
            identified_files.get("filesToGenerate", [])
        )
        DebugFileLogger.log_to_file(
            f"file_categories_{request_id}.json", categorized_files
        )

        # 7. Generate code
        code = await code_generator.generate_code(
            detailed_prd,
            design_system_code,
            identified_files,
            request,
            log_picker_callback,
        )

        # Adding app.tsx file to make Ui consistent
        app_tsx_file = {
            "fileName": "src/App.tsx",
            "content": react_files.get("src/App.tsx", "")
        }
        code.append(app_tsx_file)

        DebugFileLogger.log_to_file(f"react_code_{request_id}.json", code)
        logger.info("Code | Generation complete ✓")
        logger.debug(f"Code | Complete | Time: {datetime.datetime.now() - start_time}")

        if not code:
            logger.error("Code | Empty output X")
            raise HTTPException(status_code=500, detail="Failed to generate React code")

        # Calculate statistics
        total_files = len(identified_files.get("filesToGenerate", []))
        generated_files = len(code)
        success_rate = (generated_files / total_files) * 100 if total_files > 0 else 0

        # Check for error markers
        error_files = []
        for file_info in code:
            if "// ERROR:" in file_info.get("content", ""):
                error_files.append(file_info.get("fileName", "unknown"))

        # Final summary
        duration = datetime.datetime.now() - start_time
        logger.info(
            f"COMPLETE | Generated {generated_files}/{total_files} files ({success_rate:.1f}%) in {duration}"
        )

        if error_files:
            logger.warning(f"Errors | {len(error_files)} files contain errors X")
            logger.debug(f"Error files: {error_files}")


        await build_and_deploy(
            states,
            code,
            repo_name,
            project_id,
            request_id,
            request,
            ado_helper,
            redis_stream_publisher,
            "initial-code-gen",
        )

        await persist_data_to_db(states, states[-1], project_id, request_id)

    except Exception as e:
        logger.error("Unexpected error while creating your project :", e)
        await redis_stream_publisher.publish_project_update(request_id, state)
        await update_project_status_v2(
            project_id,
            request_id,
            ProjectStatusEnum.FAILED,
            f"Unexpected error while creating your project : {e}",
            AppProgressEnum.FAILED,
            f"Oops! Something unexpected happened while I was working on '{request.get('project_name', 'your project')}'. Don't worry though - these things happen! Just give it another shot and I'll get right back to crafting your amazing app. I'm ready when you are!",
            json.dumps(states),
            json.dumps(state["metadata"]),
        )


async def create_new_project_core(
    request: ProjectCreateRequest, user: int, user_signature: str
) -> CodeResponse:
    id = await create_project(
        request.project_name,
        request.project_description,
        request.project_type,
        user,
        request.project_state,
    )
    return CodeResponse(status_code=200, code=str(id))


@router.post("/create")
async def create_new_project(
    request: ProjectCreateRequest,
    user: int = Depends(get_current_user),
    user_signature: str = Query(...),
):
    return await create_new_project_core(request, user, user_signature)


@router.get("/status")
async def get_generation_status(
    project_id: str = Query(...), status_id: str = Query(...)
):
    status = await get_project_status_v2(project_id, status_id)
    project_status = ProjectStatusV2(
        progress=status["progress"],
        status=status["status"],
        log=status["log"],
        progress_description=status["progress_description"],
        history=status["history"],
        metadata=status["metadata"],
    )
    history = json.loads(project_status.history)
    metadata = json.loads(project_status.metadata)

    prev_metadata = []
    if len(history) > 1:
        if history[-1]["progress"] == "DEPLOY" and history[-1]["status"] == "COMPLETED":
            prev_metadata = history[-2]["metadata"]
        else:
            prev_metadata = history[-1]["metadata"]

    new_status = {
        "progress": status["progress"],
        "status": status["status"],
        "log": status["log"],
        "progress_description": status["progress_description"],
        "history": history,
        "metadata": metadata,
        "prev_metadata": prev_metadata,
    }

    return JobStateResponse(status_code=200, details=new_status)


@handle_exceptions_and_log(logger)
@router.post("/regenerate/code")
async def regenerate_code(
    request: CodeRegenerationRequest,
    background_tasks: BackgroundTasks,
    request_obj: Request,
    user_signature: str = Query(..., description="User's email or identifier"),
    project_id: str = Query(..., description="Project ID"),
    request_id: str = Query(..., description="Request ID"),
) -> JSONResponse:
    """
    Process a code regeneration request using the EE_MLO_REGENERATION mode with SSE support.
    Process a code regeneration request using the EE_MLO_REGENERATION mode with SSE support.

    Receives a request with original code and user request, processes it in the background,
    and provides real-time updates via SSE.
    Receives a request with original code and user request, processes it in the background,
    and provides real-time updates via SSE.

    Args:
        request: The request containing the prompt (stringified JSON with
                 original code and user request) and optionally image.
        background_tasks: FastAPI background tasks for async processing.
        request_obj: FastAPI request object to access app state.
        background_tasks: FastAPI background tasks for async processing.
        request_obj: FastAPI request object to access app state.
        user_signature: User's email or identifier from query parameter.
        project_id: Project ID from query parameter.
        request_id: Request ID from query parameter.
        project_id: Project ID from query parameter.
        request_id: Request ID from query parameter.

    Returns:
        JSONResponse with job_id and project_id for SSE tracking.
        JSONResponse with job_id and project_id for SSE tracking.
    """

    logger.info(f"✓ Code regeneration request received from {user_signature}")
    logger.debug(f"Request contains image: {bool(request.image)}")

    try:
        event_type = "code-regen"

        # Get the user details
        user = await get_or_create_user(user_signature)

        project = await get_project_details(project_id)

        repo_name = f"{project['project_name'].lower().replace(' ', '-')}-{str(user)[-4:]}-{project_id[-4:]}"

        # Get project status to retrieve existing states
        project_status = await get_project_status_v2(
            project_id=project_id, status_id=request_id
        )

        states = (
            json.loads(project_status["history"])
            if project_status.get("history")
            else []
        )

        redis_publisher: RedisStreamPublisher = (
            request_obj.app.state.redis_stream_publisher
        )

        state = {
            "status": ProjectStatusEnum.IN_PROGRESS,
            "progress": AppProgressEnum.CODE_GENERATION,
            "log": "Code Regeneration Agent | Regenerating code based on your request",
            "progress_description": "Hey! I got your message and I'm excited to help! I'm diving right into your code to make those changes you requested. This is going to be great - I love fine-tuning things to make them exactly how you envision them!",
            "event_type": event_type,
            "metadata": [],
        }
        states = prepare_states(states, state)
        await redis_publisher.publish_project_update(request_id, state)

        # Add background task for processing regeneration
        background_tasks.add_task(
            process_code_regeneration,
            request,
            states,
            project_id,
            request_id,
            user_signature,
            user,
            repo_name,
            redis_publisher,
            event_type,
        )

        return JSONResponse(
            status_code=202, content={"job_id": request_id, "project_id": project_id}
        )

    except Exception as e:
        logger.error(f"Error initiating code regeneration: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to initiate code regeneration: {str(e)}",
        )


async def process_code_regeneration(
    request,
    states,
    project_id,
    request_id,
    user_signature,
    user,
    repo_name,
    redis_stream_publisher: RedisStreamPublisher,
    event_type,
):
    """
    Background task to process code regeneration with SSE updates.
    """
    try:
        event_type = "code-regen"
        start_time = datetime.datetime.now()

        # Parse the original prompt JSON to get the original code
        original_code = safe_json_parse(request.prompt, "code")

        # Prepare the request dictionary for the service layer
        service_request_dict = {
            "prompt": request.prompt,
            "userSignature": user_signature,
            "image": request.image,
        }

        request = request.model_dump()

        logger.debug("✓ Calling code generator service...")

        # Process the request using the code generator service
        api_response = await code_generator.regenerate_code(service_request_dict)

        # Use the post-processor to extract the code list from the potentially complex API response
        logger.debug("✓ Starting code extraction from API response...")
        extracted_code_list = extract_code_from_api_response(api_response)

        # Merge the original code with the new code, prioritizing new code
        logger.debug("✓ Starting code merge process...")
        updated_code_list = merge_code_lists(original_code, extracted_code_list)

        logger.info(f"✓ Code regeneration complete - {len(updated_code_list)} files")
        end_time = datetime.datetime.now()
        logger.debug(f"Time taken: {end_time - start_time}")
        state = states.pop()
        state["status"] = ProjectStatusEnum.COMPLETED
        state["log"] = "Code Regeneration Agent | Regeneration successful"
        state["progress_description"] = (
            "Fantastic! 🎉 I've successfully made all the changes you requested! Your code is now updated and looking better than ever. I really enjoyed working on this with you - everything should be exactly how you wanted it!"
        )
        state["metadata"] = [{"type": "files", "data": updated_code_list}]
        state["event_type"] = event_type

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        ado_helper = AdoHelper(repo_name, "azure-pipelines.yaml")

        await build_and_deploy(
            states,
            updated_code_list,
            repo_name,
            project_id,
            request_id,
            request,
            ado_helper,
            redis_stream_publisher,
            event_type,
        )

        await persist_data_to_db(states, states[-1], project_id, request_id)

        # State 1: BUILD - Code generation and commit

    except Exception as e:
        logger.error(f"Error during code regeneration: {str(e)}", exc_info=True)

        # Error state
        state = {
            "status": ProjectStatusEnum.FAILED,
            "progress": AppProgressEnum.FAILED,
            "log": "Code Regeneration Agent | Encountered an unexpected issue",
            "progress_description": "Hmm, I hit a little snag while working on your code updates! Don't worry though - sometimes these things happen when we're pushing the boundaries of what's possible. Let's give it another try, and I'll approach it from a different angle this time!",
            "metadata": [],
        }
        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )


@handle_exceptions_and_log(logger)
@router.post("/error/build")
async def fix_build(
    request: CodeRegenerationRequest,
    background_tasks: BackgroundTasks,
    request_obj: Request,
    project_id: str = Query(...),
    request_id: str = Query(...),
    user_signature: str = Query(...),
):
    states = []
    try:
        # Get the user details
        user = await get_or_create_user(user_signature)

        project_status = await get_project_status_v2(
            project_id=project_id, status_id=request_id
        )

        project = await get_project_details(project_id)

        repo_name = f"{project['project_name'].lower().replace(' ', '-')}-{str(user)[-4:]}-{project_id[-4:]}"

        states = json.loads(project_status["history"])
        is_regeneration = any(
            s.get("progress") == AppProgressEnum.DEPLOY
            and s.get("status") == ProjectStatusEnum.COMPLETED
            for s in states
        )
        event_type = "code-regen" if is_regeneration else "initial-code-gen"
        redis_publisher = request_obj.app.state.redis_stream_publisher

        log_message = "Regenerating code based on your request"
        log_message = (
            f"Error Fix Agent | {log_message} "
            if event_type == "code-regen"
            else f"Lead Developer | {log_message}"
        )

        if event_type == "code-regen":
            state = {
                "status": ProjectStatusEnum.IN_PROGRESS,
                "progress": AppProgressEnum.CODE_GENERATION,
                "log": log_message,
                "progress_description": "I see there's an issue to fix! No worries at all - I'm like a digital detective when it comes to solving these kinds of problems. Let me dive in and get this sorted out for you!",
                "event_type": event_type,
                "metadata": [],
            }

        else:
            state = {
                "status": ProjectStatusEnum.IN_PROGRESS,
                "progress": AppProgressEnum.PAGES_GENERATED,
                "log": log_message,
                "progress_description": "I spotted something that needs fixing! Perfect timing - I love solving these kinds of challenges. Let me work my magic and get your project back on track!",
                "event_type": event_type,
                "metadata": [],
            }

        states = prepare_states(states, state)
        await redis_publisher.publish_project_update(request_id, state)

        background_tasks.add_task(
            process_error,
            request,
            states,
            project_id,
            request_id,
            user_signature,
            user,
            repo_name,
            redis_publisher,
            event_type,
        )
        return JSONResponse(
            status_code=202, content={"job_id": request_id, "project_id": project_id}
        )
    except Exception as e:
        await update_project_status_v2(
            project_id,
            request_id,
            ProjectStatusEnum.FAILED,
            f"Unexpected error while updating your project : {e}",
            AppProgressEnum.FAILED,
            f"Oops! Something unexpected happened while I was updating your project. No biggie though - let's give it another shot! I'm ready to tackle this challenge again with fresh energy!",
            json.dumps(states),
            json.dumps([]),
        )


async def process_error(
    request,
    states,
    project_id,
    request_id,
    user_signature,
    user,
    repo_name,
    redis_stream_publisher,
    event_type,
):
    """
    Enhanced process_error function with detailed SSE state updates.
    """
    try:
        # Parse the original prompt JSON to get the original code
        original_code = safe_json_parse(request.prompt, "code")

        # Prepare the request dictionary for the service layer
        service_request_dict = {
            "prompt": request.prompt,
            "userSignature": user_signature,
            "image": None,
        }

        request = request.model_dump()

        logger.debug("✓ Calling code generator service for error fixing...")

        # Process the request using the code generator service
        api_response = await code_generator.regenerate_code(service_request_dict)

        # Use the post-processor to extract the code list from the potentially complex API response
        logger.debug("✓ Starting code extraction from API response...")
        extracted_code_list = extract_code_from_api_response(api_response)

        # Merge the original code with the new code, prioritizing new code
        logger.debug("✓ Starting code merge process...")
        updated_code_list = merge_code_lists(original_code, extracted_code_list)

        logger.info(f"✓ Error fixes applied - {len(updated_code_list)} files updated")

        state = states.pop()
        state["status"] = ProjectStatusEnum.COMPLETED
        if event_type == "code-regen":
            state["log"] = (
                "Error Fix Agent | Success! I've identified and resolved the issue, ensuring everything is back on track."
            )
        else:
            state["log"] = (
                "Success! I've identified and resolved the issue, ensuring everything is back on track."
            )

        if event_type == "initial-code-gen":
            state["progress_description"] = (
                "Victory! 🎉 I found the issue and fixed it perfectly! Your project is now running smoothly and everything is exactly as it should be. I love those satisfying moments when everything clicks into place!"
            )

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        ado_helper = AdoHelper(repo_name, "azure-pipelines.yaml")

        # Initialize GitHub connector and commit files
        await build_and_deploy(
            states,
            updated_code_list,
            repo_name,
            project_id,
            request_id,
            request,
            ado_helper,
            redis_stream_publisher,
            event_type,
        )
        await persist_data_to_db(states, states[-1], project_id, request_id)
    except Exception as e:
        logger.error(
            f"Error during build error fixing process: {str(e)}", exc_info=True
        )

        # Error state
        state = {
            "status": ProjectStatusEnum.FAILED,
            "progress": AppProgressEnum.FAILED,
            "log": f"Error Fix Agent | Error during fix process: {str(e)}",
            "progress_description": "Uh oh! I ran into a hiccup while trying to fix those build issues. Sometimes the trickiest problems need a fresh approach! Let's take a step back and try again - I'm confident we'll get this sorted out together!",
            "event_type": event_type,
            "is_final": True,
            "metadata": [],
        }
        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )


def prepare_states(states, state):
    if len(states) > 0 and states[-1]["progress"] == state["progress"]:
        states.pop()

    states.append(state)

    return states


async def persist_data_to_db(states: list, state, project_id, request_id):
    states = prepare_states(states, state)
    await update_project_status_v2(
        project_id,
        request_id,
        state["status"],
        state["log"],
        state["progress"],
        state["progress_description"],
        json.dumps(states),
        json.dumps(state["metadata"]),
    )

    return states


async def build_and_deploy(
    states,
    code,
    repo_name,
    project_id,
    request_id,
    request,
    ado_helper,
    redis_stream_publisher,
    event_type,
):
    try:
        if "framework" not in request:
            request["framework"] = "react"
            logger.info("Defaulted missing 'framework' key to 'react'.")
        if "design_library" not in request:
            request["design_library"] = "tailwindcss"
            logger.info("Defaulted missing 'design_library' key to 'tailwindcss'.")

        logger.info(
            f"Committing {len(code)} generated files to Azure DevOps repository: {repo_name}"
        )

        # Ensure we have the repository name from the initial creation
        if not repo_name:
            logger.error("Repository name is not available for commit operation")
            raise ValueError("Repository name is required for commit operation")

        # Simple commit - just push the generated code to the existing repo
        commit_hash = await ado_helper.commit_multiple_files(
            repo_name=repo_name,
            files_to_commit=code,
            commit_message=f"🚀 Generated the application code with {request['design_library']} components [trigger-pipeline]",
            branch="main",
            operation_type="mixed",  # Mixed mode - detect if files exist and use add/edit accordingly
        )

        if commit_hash:
            logger.info(
                f"✅ Successfully committed generated code. Commit hash: {commit_hash}"
            )
            logger.info(
                f"🔄 Azure Pipeline should now be triggered automatically for repository: {repo_name}"
            )

            log_message = "Securing your {request['framework']} codebase"

            log_message = (
                f"Code Regeneration Agent | {log_message} "
                if event_type == "code-regen"
                else f"Version Control Specialist | {log_message}"
            )

            state = {
                "status": ProjectStatusEnum.IN_PROGRESS,
                "progress": AppProgressEnum.BUILD,
                "log": log_message,
                "progress_description": f"Awesome! Your {request.get('framework', 'React')} code is now safely stored in version control with full change tracking. I love this part - watching your project transform from idea to reality! Now let's get this baby built and ready to show off to the world!",
                "event_type": event_type,
                "metadata": [{"type": "files", "data": code}],
            }

            states = await publish_and_get_state(
                redis_stream_publisher, request_id, state, states
            )

        else:
            logger.error("❌ Commit operation failed - no commit hash returned")
            raise Exception("Failed to commit code to repository")

    except Exception as e:
        logger.error(f"❌ Failed to commit generated code to Azure DevOps: {str(e)}")
        import traceback

        logger.error(f"Commit error traceback: {traceback.format_exc()}")
        # Re-raise the exception to ensure the process fails if commit fails
        raise Exception(f"Code generation completed but commit failed: {str(e)}")

    # Polling for build status
    if commit_hash and commit_hash != "-1":
        print(f"Polling build status for commit: {commit_hash}")
        build_status_result = await ado_helper.poll_build_status(
            commit_sha=commit_hash,
            repository_name=repo_name,
            timeout_minutes=10,  # e.g. 20 minutes
            poll_interval_seconds=10,
        )

        final_status = build_status_result.get("status")
        final_result = build_status_result.get("result")
        error_logs = build_status_result.get("logs")
        failed_step = build_status_result.get("failed_step")
        live_url = build_status_result.get("live_url")
        build_number = (
            build_status_result.get("build_number") if build_status_result else "N/A"
        )
        build_web_url = None
        if (
            build_status_result
            and build_status_result.get("_links")
            and build_status_result["_links"].get("links")
            and build_status_result["_links"]["links"].get("web")
            and build_status_result["_links"]["links"]["web"].get("href")
        ):
            build_web_url = build_status_result["_links"]["links"]["web"]["href"]

        current_metadata = []

        if final_status == "completed" and final_result == "succeeded":
            log_message = f"Build Successful with BUILD ID : {build_number}"
            log_message = (
                f"Code Regeneration Agent | {log_message} "
                if event_type == "code-regen"
                else f"DevOps Specialist | {log_message}"
            )
            state = {
                "status": ProjectStatusEnum.COMPLETED,
                "progress": AppProgressEnum.BUILD,
                "log": log_message,
                "progress_description": f"YES! 🎉 Build #{build_number} completed successfully! Your application compiled beautifully - no errors, no warnings, just pure, clean code ready to shine! This is always such a satisfying moment!",
                "metadata": [],
            }

            states = await publish_and_get_state(
                redis_stream_publisher, request_id, state, states
            )

            log_message = "Deployment in progress"
            log_message = (
                f"Code Regeneration Agent | {log_message} "
                if event_type == "code-regen"
                else f"DevOps Specialist | {log_message}"
            )

            state = {
                "status": ProjectStatusEnum.IN_PROGRESS,
                "progress": AppProgressEnum.DEPLOY,
                "log": log_message,
                "progress_description": f"The moment we've all been waiting for! I'm deploying your beautiful {request.get('framework', 'React')} application to the cloud right now. In just a few moments, your vision will be live on the internet for the whole world to see!",
                "metadata": [{"type": "ref_code", "data": live_url}],
            }

            states = await publish_and_get_state(
                redis_stream_publisher, request_id, state, states
            )

            await asyncio.sleep(5)
            # log_msg = f"Deployment Successful (Build #{build_number})"
            desc_msg = f"Your application has been successfully built and deployed! You can view it live here: {live_url}."
            if build_web_url:
                desc_msg += f" See Azure DevOps Build: {build_web_url}"
                current_metadata.append(
                    {"type": "ado_build_url", "data": build_web_url}
                )
            if live_url:
                current_metadata.append({"type": "ref_code", "data": live_url})

                log_message = f"Your application has been successfully built and deployed! You can view it live here: {live_url}"
                log_message = (
                    f"Code Regeneration Agent | {log_message} "
                    if event_type == "code-regen"
                    else f"DevOps Specialist | {log_message}"
                )

                state = {
                    "status": ProjectStatusEnum.COMPLETED,
                    "progress": AppProgressEnum.DEPLOY,
                    "log": log_message,
                    "is_final": True,
                    "progress_description": f"🎊 WOW! We did it! Your application is now LIVE and looking absolutely stunning! I'm genuinely excited for you - this is such an amazing accomplishment! Click over to the preview tab to see your creation in all its glory.",
                    "metadata": [{"type": "ref_code", "data": live_url}],
                }

                states = await publish_and_get_state(
                    redis_stream_publisher, request_id, state, states
                )

        else:
            log_message = f"Deployment failed with status: {final_status}, result: {final_result}."
            progress_description = f"The deployment failed at step: '{failed_step}'. Please check the logs for more details."
            if error_logs:
                log_message += "\nSee logs for details."
                progress_description += f"\n\nBuild Error:\n```\n{error_logs}\n```"

            log_message = error_logs
            log_message = (
                f"Code Regeneration Agent | {log_message} "
                if event_type == "code-regen"
                else f"DevOps Specialist | {log_message}"
            )

            state = {
                "status": ProjectStatusEnum.FAILED,
                "progress": AppProgressEnum.BUILD,
                "log": log_message,
                "progress_description": f"Hmm, we hit a bump during the build process! 🛠️ Don't worry - this happens to the best of us! The build failed at '{failed_step}', but I've got the detailed logs ready to help us figure this out. Let's take a look at what happened and get this fixed together!",
                "event_type": event_type,
                "metadata": [],
            }

            states = await publish_and_get_state(
                redis_stream_publisher, request_id, state, states
            )

        state["event_type"] = event_type
        states = prepare_states(states, state)


async def publish_and_get_state(redis_stream_publisher, request_id, state, states):
    await redis_stream_publisher.publish_project_update(request_id, state)
    return prepare_states(states, state)
