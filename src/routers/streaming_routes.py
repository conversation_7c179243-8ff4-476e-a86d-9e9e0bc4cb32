# In an appropriate router file (e.g., api/streaming_routes.py or your main router file)

from fastapi import APIRouter, Request, HTTPException, Depends
from sse_starlette.sse import EventSourceResponse
import redis.asyncio as redis  # For type hinting if needed, actual client from app.state
import asyncio
import json
import logging

# If you have a shared router instance, use it. Otherwise, create a new one.
# from .your_main_router_file import router # Example if router is defined elsewhere
# OR
streaming_router = APIRouter()

logger = logging.getLogger(__name__)  # Use the appropriate logger for your project


# Dependency to get the Redis client (if you want to be explicit, though request.app.state.redis_client is also fine)
async def get_redis_client(request: Request) -> redis.Redis:
    redis_client = request.app.state.redis_client  # As set up in your lifespan
    if not redis_client:
        raise HTTPException(
            status_code=503, detail="Redis service not configured or unavailable."
        )
    return redis_client


@streaming_router.get("/project-status/{request_id}")
async def stream_project_status(
    request: Request,  # FastAPI request object
    request_id: str,  # The unique ID for the generation job (job_id)
    redis_client: redis.Redis = Depends(get_redis_client),  # Inject Redis client
):
    """
    Server-Sent Events endpoint to stream project generation status updates.
    Clients connect to this endpoint using the `request_id` (or `job_id`)
    obtained from the `/generate/app` response.
    """
    stream_name = (
        f"project_updates:{request_id}"  # Must match the publisher's stream_name logic
    )

    async def event_generator():
        # Check for the Last-Event-ID header sent by the browser on reconnect
        last_event_id = request.headers.get("last-event-id")
        # Allow client to request history replay via a query parameter
        since = request.query_params.get("since")
        requested_event_type = request.query_params.get("event_type")

        if since in ("0", "all"):
            # Start from the very beginning of the stream.
            last_id_processed = "0-0"
            logger.info(
                f"Replaying entire stream {stream_name} from the beginning ('0-0')."
            )
        elif last_event_id:
            # If client is reconnecting, resume from where it left off.
            last_id_processed = last_event_id
            logger.info(
                f"Resuming stream {stream_name} from last event ID: {last_event_id}"
            )
        else:
            # If it's a new connection, start from the latest message.
            last_id_processed = "$"
            logger.info(f"Starting new stream {stream_name} from the end ('$').")

        # Check if the stream exists. If not, the background task might not have started
        # or published the first message yet. XREAD will block or return empty if stream doesn't exist
        # or has no new messages.
        num_existing_streams = await redis_client.exists(stream_name)
        stream_exists = num_existing_streams > 0
        if not stream_exists:
            logger.info(
                f"Stream {stream_name} does not exist yet for request_id {request_id}. Client will wait."
            )
            # Optionally, send an initial "waiting" message
            # yield {"event": "info", "data": json.dumps({"message": "Waiting for process to start..."})}

        try:
            while True:
                # 1. Check if the client has disconnected
                if await request.is_disconnected():
                    logger.info(
                        f"Client disconnected from SSE stream for request_id: {request_id}"
                    )
                    break  # Exit the generator loop

                # 2. Read from the Redis Stream
                #    XREAD [COUNT count] [BLOCK milliseconds] STREAMS key [key ...] id [id ...]
                #    BLOCK 0 means non-blocking. BLOCK > 0 means wait for 'milliseconds'.
                #    Using a block timeout allows the loop to periodically check for client disconnect.
                messages = await redis_client.xread(
                    streams={stream_name: last_id_processed},
                    count=5,  # Read up to 5 messages at a time (adjust as needed)
                    block=1000,  # Block for up to 1000ms (1 second) if no new messages
                )

                if not messages:  # Timeout, no new messages
                    # logger.debug(f"No new messages in stream {stream_name} for request_id: {request_id}, last_id: {last_id_processed}")
                    # Optionally send a keep-alive ping if your client needs it, though SSE usually handles this.
                    # yield {"event": "ping", "data": "keep-alive"}
                    await asyncio.sleep(
                        0.1
                    )  # Small sleep before trying again to prevent tight loop on error or fast disconnect
                    continue

                for (
                    stream,
                    msg_list,
                ) in messages:  # Should only be one stream (stream_name)
                    for message_id, message_data_fields in msg_list:
                        if (
                            not message_data_fields
                        ):  # Should not happen with valid stream entries
                            logger.warning(
                                f"Empty message fields for ID {message_id} in stream {stream_name}"
                            )
                            last_id_processed = message_id  # Still advance ID
                            continue

                        # Assuming your RedisStreamPublisher stores the JSON payload in a field named "data"
                        json_payload = message_data_fields.get("data")
                        if not json_payload:
                            logger.warning(
                                f"Message ID {message_id} in stream {stream_name} missing 'data' field."
                            )
                            last_id_processed = message_id
                            continue

                        try:
                            # The actual data for the SSE event
                            event_payload_dict = json.loads(json_payload)
                        except json.JSONDecodeError as e:
                            logger.error(
                                f"Failed to decode JSON from stream {stream_name}, ID {message_id}: {e}"
                            )
                            last_id_processed = message_id
                            continue

                        # Determine event type based on operation
                        event_type = "initial-code-gen"  # default

                        if (
                            event_payload_dict.get("progress")
                            in [
                                "OVERVIEW",
                                "SEED_PROJECT_INITIALIZED",
                                "LAYOUT_ANALYZED",
                                "COMPONENTS_CREATED",
                                "PAGES_GENERATED",
                                "DESIGN_SYSTEM_MAPPED",
                                "FILES_GENERATED",
                                "BUILD",
                                "DEPLOY",
                                "FILE_QUEUE",
                                "COMPLETED",
                                "FAILED",
                            ]
                            and "regeneration"
                            not in event_payload_dict.get("log", "").lower()
                            and "error fix"
                            not in event_payload_dict.get("log", "").lower()
                        ):
                            event_type = "initial-code-gen"

                        # Check for regeneration or error fixing
                        elif (
                            event_payload_dict.get("progress")
                            in [
                                "CODE_GENERATION" "BUILD",
                                "DEPLOY",
                                "FAILED",
                            ]
                            and "regeneration"
                            in event_payload_dict.get("log", "").lower()
                            or "error fix" in event_payload_dict.get("log", "").lower()
                            or "Code Regeneration Agent"
                            in event_payload_dict.get("log", "")
                            or "Error Fix Agent" in event_payload_dict.get("log", "")
                        ):
                            event_type = "code-regen"

                        # Allow manual override via event_type field
                        if "event_type" in event_payload_dict:
                            event_type = event_payload_dict["event_type"]

                        if requested_event_type and event_type != requested_event_type:
                            # Still need to advance the message ID to avoid re-reading it
                            last_id_processed = message_id
                            continue

                        # Yield the event in SSE format
                        # `event` is the event type (client can listen for specific types)
                        # `data` is the payload (must be a string)
                        # `id` is the message ID (helps client resume if connection drops)
                        yield {
                            "event": event_type,
                            "id": message_id.decode("utf-8")
                            if isinstance(message_id, bytes)
                            else message_id,
                            "data": json.dumps(
                                event_payload_dict
                            ),  # Send the full deserialized dict as a JSON string
                        }

                        last_id_processed = (
                            message_id  # Update the last ID we've processed
                        )

                        # Check if this is the final message
                        if (
                            event_payload_dict.get("is_final", False) is True
                            and event_type == "initial-code-gen"
                        ):
                            logger.info(
                                f"Final message processed for request_id: {request_id}. Closing SSE stream."
                            )
                            return  # End the generator, which closes the SSE connection

                # Small sleep if messages were processed, to yield control
                await asyncio.sleep(0.01)

        except redis.RedisError as e:
            logger.error(
                f"Redis error during SSE streaming for request_id {request_id}: {e}",
                exc_info=True,
            )
            # Send an error event to the client before closing
            yield {
                "event": "error",
                "id": "redis-error",
                "data": json.dumps(
                    {"message": "A Redis error occurred. Please try reconnecting."}
                ),
            }
        except asyncio.CancelledError:
            logger.info(
                f"SSE stream task cancelled for request_id: {request_id} (likely client disconnect)."
            )
            # This is expected when the client disconnects or server shuts down
        except Exception as e:
            logger.error(
                f"Unexpected error in SSE event generator for request_id {request_id}: {e}",
                exc_info=True,
            )
            # Send an error event to the client
            yield {
                "event": "error",
                "id": "internal-server-error",
                "data": json.dumps({"message": "An unexpected server error occurred."}),
            }
        finally:
            logger.info(f"SSE event generator for request_id {request_id} finished.")
            # Any cleanup specific to this generator could go here, but usually not needed.

    # EventSourceResponse handles the SSE protocol details
    return EventSourceResponse(event_generator())
