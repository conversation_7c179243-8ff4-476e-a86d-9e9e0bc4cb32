import uuid
from datetime import datetime
import asyncpg
from src.services.database.db import get_connection


async def create_user(username, email):
    try:
        con = await get_connection()
        user_id = str(uuid.uuid4())
        created_at = datetime.utcnow()

        await con.execute(
            """
            INSERT INTO users (user_id, username, email, created_at)
            VALUES ($1, $2, $3, $4)
            """,
            user_id,
            username,
            email,
            created_at,
        )

        await con.close()
        return user_id
    except asyncpg.PostgresError as e:
        raise e


async def create_project(
    project_name, project_description, project_type, created_by, project_state
):
    try:
        con = await get_connection()
        project = await con.fetchrow(
            """
            INSERT INTO projects (project_name, project_description, project_type, created_by, project_state)
            VALUES ($1, $2, $3, $4, $5) RETURNING project_id
            """,
            project_name,
            project_description,
            project_type,
            created_by,
            project_state,
        )
        await con.close()
        return project["project_id"]
    except asyncpg.PostgresError as e:
        raise e


async def get_project(project_id):
    try:
        # Fast UUID validation and conversion
        if isinstance(project_id, str):
            try:
                # Try to parse as UUID
                uuid.UUID(project_id)
            except ValueError:
                # Not a valid UUID, return None immediately
                return None

        con = await get_connection()
        project = await con.fetchrow(
            "SELECT * FROM projects WHERE project_id = $1", project_id
        )
        await con.close()
        return project
    except asyncpg.PostgresError:
        return None


async def create_project_image(project_id, image_url):
    try:
        con = await get_connection()
        await con.execute(
            """
            INSERT INTO project_images (project_id, image_url)
            VALUES ($1, $2)
            """,
            project_id,
            image_url,
        )
        await con.close()
    except asyncpg.PostgresError as e:
        raise e


async def create_project_conversation(project_id, user_query, ai_response):
    try:
        con = await get_connection()
        await con.execute(
            """
            INSERT INTO project_conversations (project_id, user_query, ai_response)
            VALUES ($1, $2, $3)
            """,
            project_id,
            user_query,
            ai_response,
        )
        await con.close()
    except asyncpg.PostgresError as e:
        raise e


async def create_code_version(project_id, is_full_snapshot, code_snapshot):
    try:
        con = await get_connection()
        await con.execute(
            """
            INSERT INTO code_versions (project_id, is_full_snapshot, code_snapshot)
            VALUES ($1, $2, $3)
            """,
            project_id,
            is_full_snapshot,
            code_snapshot,
        )
        await con.close()
    except asyncpg.PostgresError as e:
        raise e


async def create_code_diff(version_id, file_path, diff_data):
    try:
        con = await get_connection()
        await con.execute(
            """
            INSERT INTO code_diffs (version_id, file_path, diff_data)
            VALUES ($1, $2, $3)
            """,
            version_id,
            file_path,
            diff_data,
        )
        await con.close()
    except asyncpg.PostgresError as e:
        raise e


async def get_or_create_user(user_email: str):
    try:
        con = await get_connection()
        user = await con.fetchrow(
            "SELECT user_id FROM users WHERE email = $1", user_email
        )
        if not user:
            user_id = str(uuid.uuid4())
            await con.execute(
                "INSERT INTO users (user_id, username, email, created_at) VALUES ($1, $2, $3, NOW())",
                user_id, user_email.split("@")[0], user_email
            )
            await con.close()
            return user_id
        await con.close()
        return user["user_id"]
    except asyncpg.PostgresError:
        return None


async def get_project_and_user_fast(project_id: str, user_email: str):
    """Fast combined lookup for project and user - single connection"""
    try:
        # Fast UUID validation
        if isinstance(project_id, str):
            try:
                uuid.UUID(project_id)
            except ValueError:
                return None, None

        con = await get_connection()

        # Get both project and user in parallel queries on same connection
        project_task = con.fetchrow("SELECT * FROM projects WHERE project_id = $1", project_id)
        user_task = con.fetchrow("SELECT user_id FROM users WHERE email = $1", user_email)

        project, user = await asyncio.gather(project_task, user_task)

        # If user doesn't exist, create it quickly
        if not user:
            user_id = str(uuid.uuid4())
            await con.execute(
                "INSERT INTO users (user_id, username, email, created_at) VALUES ($1, $2, $3, NOW())",
                user_id, user_email.split("@")[0], user_email
            )
            user_id_result = user_id
        else:
            user_id_result = user["user_id"]

        await con.close()
        return project, user_id_result
    except asyncpg.PostgresError:
        return None, None


async def get_project_status(project_id, status_id):
    try:
        con = await get_connection()
        status = await con.fetchrow(
            """
            SELECT progress, status, log, progress_description
            FROM project_status
            WHERE project_id = $1 AND status_id = $2
            """,
            project_id,
            status_id,
        )
        await con.close()
        return {
            "progress": status["progress"],
            "status": status["status"],
            "log": status["log"],
            "progress_description": status["progress_description"],
        }
    except asyncpg.PostgresError as e:
        raise e


async def get_project_status_v2(project_id, status_id):
    try:
        con = await get_connection()
        status = await con.fetchrow(
            """
            SELECT progress, status, log, progress_description, history, metadata
            FROM project_status_v2
            WHERE project_id = $1 AND status_id = $2
            """,
            project_id,
            status_id,
        )
        await con.close()
        return {
            "progress": status["progress"],
            "status": status["status"],
            "log": status["log"],
            "progress_description": status["progress_description"],
            "history": status["history"],
            "metadata": status["metadata"],
        }
    except asyncpg.PostgresError as e:
        raise e


async def update_project_status(
    project_id, status_id, status, log_message, progress, progress_description
):
    try:
        con = await get_connection()
        await con.execute(
            """
            UPDATE project_status
            SET status = $4, log = $5, progress = $3, progress_description = $6, updated_at = NOW()
            WHERE project_id = $2 AND status_id = $1
            """,
            status_id,
            project_id,
            progress,
            status,
            log_message,
            progress_description,
        )
        await con.close()
    except asyncpg.PostgresError as e:
        raise e


async def update_project_status_v2(
    project_id,
    status_id,
    status,
    log_message,
    progress,
    progress_description,
    history,
    metadata,
):
    try:
        con = await get_connection()
        await con.execute(
            """
            UPDATE project_status_v2
            SET status = $4, log = $5, progress = $3, progress_description = $6, history = $7, metadata = $8, updated_at = NOW()
            WHERE project_id = $2 AND status_id = $1
            """,
            status_id,
            project_id,
            progress,
            status,
            log_message,
            progress_description,
            history,
            metadata,
        )
        await con.close()
    except asyncpg.PostgresError as e:
        raise e


async def create_project_status(
    project_id, status_id, status, log_message, progress, progress_description
):
    try:
        con = await get_connection()
        await con.execute(
            """
            INSERT INTO project_status (status_id, project_id, progress, status, log, progress_description, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, NOW())
            """,
            status_id,
            project_id,
            progress,
            status,
            log_message,
            progress_description,
        )
        await con.close()
    except asyncpg.PostgresError as e:
        raise e


async def create_project_status_v2(
    project_id,
    status_id,
    status,
    log_message,
    progress,
    progress_description,
    history,
    metadata,
):
    try:
        con = await get_connection()
        await con.execute(
            """
            INSERT INTO project_status_v2 (status_id, project_id, progress, status, log, progress_description, history, metadata, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
            """,
            status_id,
            project_id,
            progress,
            status,
            log_message,
            progress_description,
            history,
            metadata,
        )
        await con.close()
    except asyncpg.PostgresError as e:
        raise e


async def get_project_details(project_id):
    try:
        con = await get_connection()
        project_details = await con.fetchrow(
            """
            SELECT project_name, project_description, last_modified
            FROM projects
            WHERE project_id = $1
            """,
            project_id,
        )
        await con.close()
        return {
            "project_name": project_details["project_name"],
            "project_description": project_details["project_description"],
            "last_modified": project_details["last_modified"],
        }
    except asyncpg.PostgresError as e:
        raise e


async def get_projects_by_user(user_id):
    try:
        con = await get_connection()
        projects = await con.fetch(
            """
            SELECT project_id, project_name, project_description, project_type, last_modified
            FROM projects
            WHERE created_by = $1
            """,
            user_id,
        )
        await con.close()
        return projects
    except asyncpg.PostgresError as e:
        raise e


async def get_project_by_user_email(user_email):
    try:
        con = await get_connection()
        user_id = await get_or_create_user(user_email)
        projects = await get_projects_by_user(user_id)
        await con.close()

        # Convert Record objects to dictionaries
        projects = [dict(project) for project in projects]

        return projects
    except asyncpg.PostgresError as e:
        raise e


async def update_project_name(project_id, new_name, description):
    try:
        con = await get_connection()
        await con.execute(
            """
            UPDATE projects
            SET project_name = $2, project_description = $3,last_modified = NOW()
            WHERE project_id = $1
            """,
            project_id,
            new_name,
            description,
        )
        await con.close()
    except asyncpg.PostgresError as e:
        raise e
