# src/services/azure/ad_repo_connector.py
import os
import asyncio
import base64
import re
import shutil
import subprocess
import sys
import time
from typing import List, Dict, Optional, Any, Tuple


from fastapi import HTTPException
from azure.devops.connection import Connection
from git import Union
from msrest.authentication import BasicAuthentication
from azure.devops.v7_1.git.models import (
    GitRepository,
    GitPush,
    GitCommitRef,
    Change,
    ItemContent,
    GitRefUpdate,
    GitItem,
    GitRepositoryCreateOptions,  # Used for new repo creation
    GitImportRequest,
    GitImportRequestParameters,
    GitImportGitSource,
)
from azure.devops.v7_1.token_admin import TokenAdminClient
from azure.devops.v7_1.core.models import TeamProjectReference
from azure.devops.v7_1.git import GitClient
from azure.devops.v7_1.task_agent import TaskAgentClient
from azure.devops.v7_1.core import CoreClient
from azure.devops.exceptions import AzureDevOpsServiceError
from azure.devops.v7_1.build.models import (
    BuildDefinition,
    BuildRepository,
    AgentPoolQueue,
    BuildDefinitionVariable,
)

from azure.devops.v7_1.build import BuildClient
from azure.devops.v7_0.service_hooks import ServiceHooksClient
from azure.devops.v7_0.service_hooks.models import (
    Subscription as ServiceHookSubscription,
)

from src.settings.settings import AzureADOConfig


MAX_PUSH_ATTEMPTS = 3
RETRY_DELAY_SECONDS = 5

ado_config = AzureADOConfig()


class AzureRepoConnector:
    def __init__(self):
        self.PAT = ado_config.ADO_PAT
        self.ORG_URL = f"https://dev.azure.com/{ado_config.ADO_ORG}"

        self.CONFIGURED_ADO_PROJECT_NAME = ado_config.ADO_PROJECT
        if not self.CONFIGURED_ADO_PROJECT_NAME:
            raise ValueError(
                "AzureADOConfig.ADO_PROJECT is not set. This must be the name of your target Azure DevOps Project."
            )

        self._commit_files: Optional[List[Dict[str, str]]] = None
        self.default_branch = "main"  # Default branch for new repos/pipelines

        self._credentials = BasicAuthentication("", self.PAT)
        self._connection = Connection(base_url=self.ORG_URL, creds=self._credentials)
        self._git_client: GitClient = self._connection.clients_v7_1.get_git_client()
        self._core_client: CoreClient = self._connection.clients_v7_1.get_core_client()
        self._build_client: BuildClient = (
            self._connection.clients_v7_1.get_build_client()
        )
        self._task_agent_client: TaskAgentClient = (
            self._connection.clients_v7_1.get_task_agent_client()
        )
        self._service_client: ServiceHooksClient = (
            self._connection.clients_v7_1.get_service_hooks_client()  # Note: v7_1 for service_hooks, user code had v7_0
        )
        self._token_client: TokenAdminClient = (
            self._connection.clients_v7_1.get_token_admin_client()
        )

        self._ado_project_guid: Optional[str] = None

    async def _ensure_ado_project_guid(self) -> str:
        if self._ado_project_guid is None:
            print(
                f"Resolving GUID for configured ADO Project: '{self.CONFIGURED_ADO_PROJECT_NAME}'"
            )
            project_details = await self.get_ado_project_details(
                self.CONFIGURED_ADO_PROJECT_NAME
            )
            self._ado_project_guid = project_details.id
            if not self._ado_project_guid:
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to resolve GUID for ADO project '{self.CONFIGURED_ADO_PROJECT_NAME}'.",
                )
            print(
                f"Configured ADO Project '{self.CONFIGURED_ADO_PROJECT_NAME}' resolved to GUID: {self._ado_project_guid}"
            )
        return self._ado_project_guid

    async def list_repositories(self):
        """List all repositories in the configured project"""
        ado_project_guid = await self._ensure_ado_project_guid()
        try:
            repositories = self._git_client.get_repositories(project=ado_project_guid)
            return repositories
        except Exception as e:
            print(f"Error listing repositories: {e}")
            raise

    async def list_pipelines(self):
        """List all pipelines in the configured project"""
        ado_project_guid = await self._ensure_ado_project_guid()
        try:
            pipelines = self._build_client.get_definitions(project=ado_project_guid)
            return pipelines
        except Exception as e:
            print(f"Error listing pipelines: {e}")
            raise

    # async def get_repository_by_name(self, repo_name: str):
    #     """Get a repository by name"""
    #     ado_project_guid = await self._ensure_ado_project_guid()
    #     try:
    #         repository = self._git_client.get_repository(
    #             repository_id=repo_name, project=ado_project_guid
    #         )
    #         return repository
    #     except Exception:
    #         # Repository not found or other error
    #         return None

    async def commit_multiple_files_to_repo(
        self,
        repo_id: str,
        files_to_commit: list,
        commit_message: str,
        branch_name: str = "main",
    ):
        """Commit multiple files to a specific repository"""
        try:
            ado_project_guid = await self._ensure_ado_project_guid()

            # Prepare changes for commit
            changes = []
            for file_info in files_to_commit:
                file_name = file_info.get("fileName", "")
                content = file_info.get("content", "")

                # Ensure content is a string
                if not isinstance(content, str):
                    content = str(content)

                # Encode content to base64
                import base64

                encoded_content = base64.b64encode(content.encode("utf-8")).decode(
                    "utf-8"
                )

                # Check if file exists to determine change type
                try:
                    self._git_client.get_item(
                        repository_id=repo_id,
                        project=ado_project_guid,
                        path=f"/{file_name}",
                        include_content=False,
                    )
                    change_type = "edit"  # File exists, so edit it
                except Exception:
                    change_type = "add"  # File doesn't exist, so add it

                changes.append(
                    {
                        "changeType": change_type,
                        "item": {"path": f"/{file_name}"},
                        "newContent": {
                            "content": encoded_content,
                            "contentType": "base64encoded",
                        },
                    }
                )

            print(f"Prepared {len(changes)} file changes for commit")

            # Create commit - use proper Git push format
            from azure.devops.v7_1.git.models import GitPush, GitRefUpdate, GitCommitRef

            # Get current branch state to handle existing branches
            try:
                refs = self._git_client.get_refs(
                    repository_id=repo_id,
                    project=ado_project_guid,
                    filter=f"heads/{branch_name}",
                )

                if refs and len(refs) > 0:
                    # Branch exists, use its current commit ID
                    old_object_id = refs[0].object_id
                    print(
                        f"Branch {branch_name} exists, updating from {old_object_id[:8]}..."
                    )
                else:
                    # New branch
                    old_object_id = "0000000000000000000000000000000000000000"
                    print(f"Creating new branch {branch_name}...")
            except Exception:
                # Assume new branch if we can't get refs
                old_object_id = "0000000000000000000000000000000000000000"
                print(f"Assuming new branch {branch_name}...")

            # Create ref update for the branch
            ref_update = GitRefUpdate(
                name=f"refs/heads/{branch_name}", old_object_id=old_object_id
            )

            # Create commit
            commit = GitCommitRef(comment=commit_message, changes=changes)

            # Create push
            push = GitPush(ref_updates=[ref_update], commits=[commit])

            # Execute the push
            push_result = self._git_client.create_push(
                push=push, repository_id=repo_id, project=ado_project_guid
            )

            if push_result and push_result.commits:
                print(f"✅ Successfully committed {len(changes)} files")
                return {"commitId": push_result.commits[0].commit_id, "success": True}
            else:
                print("❌ Push result was empty")
                return None

        except Exception as e:
            print(f"Error committing files: {e}")
            import traceback

            traceback.print_exc()
            return None

    def set_files_to_commit(self, generate_files: List[Dict[str, str]]):
        self._commit_files = generate_files

    async def get_ado_project_details(
        self, project_name_or_id: str
    ) -> TeamProjectReference:
        target_project_identifier = project_name_or_id
        print(
            f"Fetching ADO Project details for identifier: '{target_project_identifier}'"
        )
        try:
            project_details = await asyncio.to_thread(
                self._core_client.get_project, project_id=target_project_identifier
            )
            if not project_details:
                raise HTTPException(
                    status_code=404,
                    detail=f"Azure DevOps Project '{target_project_identifier}' not found (empty response).",
                )
            return project_details
        except AzureDevOpsServiceError as e:
            if (
                "TF200016" in str(e)
                or "VS402370" in str(e)
                or "does not exist" in str(e).lower()
            ):
                raise HTTPException(
                    status_code=404,
                    detail=f"Azure DevOps Project '{target_project_identifier}' does not exist. Service Error: {e}",
                )
            raise HTTPException(
                status_code=500,
                detail=f"Error accessing ADO project '{target_project_identifier}': {e}",
            )
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected error with ADO project '{target_project_identifier}'. Details: {e}",
            )

    # --- NEW METHOD: Create a new Git Repository ---
    async def create_new_repository(
        self, repo_name: str, fail_if_exists: bool = False
    ) -> GitRepository:
        """
        Creates a new, empty Git repository in the configured Azure DevOps project.

        Args:
            repo_name: The desired name for the new repository.
            fail_if_exists: If True, raises an HTTPException if the repository already exists.
                            If False, returns the existing repository.

        Returns:
            The created or existing GitRepository object.

        Raises:
            HTTPException: If the repository creation fails or if it already exists and fail_if_exists is True.
        """
        ado_project_guid = await self._ensure_ado_project_guid()
        print(
            f"--- Attempting to create or get repository '{repo_name}' in ADO Project '{self.CONFIGURED_ADO_PROJECT_NAME}' (GUID: {ado_project_guid}) ---"
        )

        async def _create_repo_sync():
            try:
                # Check if repo already exists
                existing_repo = self._git_client.get_repository(
                    repository_id=repo_name, project=ado_project_guid
                )
                print(
                    f"Repository '{repo_name}' already exists. ID: {existing_repo.id}"
                )
                if fail_if_exists:
                    raise HTTPException(
                        status_code=409,  # Conflict
                        detail=f"Repository '{repo_name}' already exists in project '{self.CONFIGURED_ADO_PROJECT_NAME}'.",
                    )
                return existing_repo
            except AzureDevOpsServiceError as e:
                # TF401019 typically means "not found"
                if "TF401019" in str(e) or "does not exist" in str(e).lower():
                    print(f"Repository '{repo_name}' not found. Proceeding to create.")
                else:
                    print(
                        f"AzureDevOpsServiceError while checking for repo '{repo_name}': {e}",
                        file=sys.stderr,
                    )
                    raise HTTPException(
                        status_code=500,
                        detail=f"Error checking for repository '{repo_name}': {str(e)}",
                    )

            # Create the repository
            print(
                f"Creating new empty Git Repository: '{repo_name}' in ADO Project '{self.CONFIGURED_ADO_PROJECT_NAME}'."
            )
            project_ref_for_ops = TeamProjectReference(id=ado_project_guid)
            # Note: The parameter name for create_repository is git_repository_to_create
            repo_create_options = GitRepositoryCreateOptions(
                name=repo_name, project=project_ref_for_ops
            )

            try:
                new_repo = self._git_client.create_repository(
                    git_repository_to_create=repo_create_options,
                    project=ado_project_guid,
                )
                print(
                    f"Successfully created empty repository '{new_repo.name}' with ID: {new_repo.id}"
                )
                print(f"Repo URL: {new_repo.remote_url}")
                print(
                    "IMPORTANT: You need to push an initial commit (including any pipeline YAML file if needed) to this repository."
                )
                print("  git init")
                print(f"  git remote add origin {new_repo.remote_url}")
                print(f"  git branch -M {self.default_branch}")
                print(
                    "  # Create your files (e.g., README.md, azure-pipelines.yaml), then:"
                )
                print("  git add .")
                print('  git commit -m "Initial commit"')
                print(f"  git push -u origin {self.default_branch}")
                return new_repo
            except AzureDevOpsServiceError as e_create:
                print(
                    f"Error creating Git Repository '{repo_name}': {e_create}",
                    file=sys.stderr,
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to create Git Repository '{repo_name}': {str(e_create)}",
                )

        try:
            return await asyncio.to_thread(_create_repo_sync)
        except HTTPException:
            raise
        except Exception as e:
            print(
                f"Unexpected error in create_new_repository for '{repo_name}': {e}",
                file=sys.stderr,
            )
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected error creating repository '{repo_name}'. Details: {e}",
            )

    # --- NEW METHOD: Create a YAML Pipeline Definition ---
    async def create_yaml_pipeline_definition(
        self,
        pipeline_name: str,
        repository_id: str,
        repository_name: str,  # Just the repo name, e.g., "my-app-repo"
        default_branch_name: str,  # e.g., "main"
        yaml_file_path: str = "azure-pipelines.yaml",  # Path within the repo
        pipeline_folder_path: str = "\\",  # Folder in ADO UI, e.g., "\\CI Pipelines"
        fail_if_exists: bool = False,
    ) -> BuildDefinition:
        """
        Creates a new YAML-based pipeline definition in Azure DevOps.
        """
        ado_project_guid = await self._ensure_ado_project_guid()
        print(
            f"--- Attempting to create or get YAML pipeline '{pipeline_name}' in ADO Project '{self.CONFIGURED_ADO_PROJECT_NAME}' (GUID: {ado_project_guid}) ---"
        )

        if not default_branch_name.startswith("refs/heads/"):
            ref_default_branch = f"refs/heads/{default_branch_name}"
        else:
            ref_default_branch = default_branch_name

        # Normalize pipeline_folder_path for consistent checks
        normalized_folder_path = "\\" + pipeline_folder_path.strip("\\")
        if normalized_folder_path == "\\":  # Root path
            ui_path_display = "root"
        else:
            ui_path_display = normalized_folder_path

        def _create_pipeline_sync():
            try:
                # Check if pipeline already exists by name and path
                existing_pipelines = self._build_client.get_definitions(
                    project=ado_project_guid,
                    name=pipeline_name,
                    path=normalized_folder_path,
                )
                if existing_pipelines:  # get_definitions returns a list
                    # It's possible for names to be non-unique if paths differ.
                    # The `path` filter in get_definitions should handle this.
                    # We take the first one if multiple are returned (should ideally be one if path is specific).
                    p = existing_pipelines[0]
                    print(
                        f"Pipeline '{pipeline_name}' already exists in folder '{ui_path_display}'. ID: {p.id}"
                    )
                    if fail_if_exists:
                        raise HTTPException(
                            status_code=409,  # Conflict
                            detail=f"Pipeline '{pipeline_name}' already exists in project '{self.CONFIGURED_ADO_PROJECT_NAME}' at path '{ui_path_display}'.",
                        )
                    return p
                print(
                    f"Pipeline '{pipeline_name}' not found at path '{ui_path_display}'. Proceeding to create."
                )
            except AzureDevOpsServiceError as e:
                print(
                    f"AzureDevOpsServiceError while checking for pipeline '{pipeline_name}': {e}",
                    file=sys.stderr,
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"Error checking for pipeline '{pipeline_name}': {str(e)}",
                )

            # Get Agent Queue
            try:
                agent_queues = self._task_agent_client.get_agent_queues(
                    project=ado_project_guid, queue_name="Azure Pipelines"
                )
                if not agent_queues:
                    all_queues = self._task_agent_client.get_agent_queues(
                        project=ado_project_guid
                    )
                    if not all_queues:
                        raise ValueError(
                            f"No agent queues found in ADO project '{self.CONFIGURED_ADO_PROJECT_NAME}'."
                        )
                    agent_queue = all_queues[0]
                    print(
                        f"Warning: 'Azure Pipelines' queue not found. Using first available queue: {agent_queue.name} (ID: {agent_queue.id})"
                    )
                else:
                    agent_queue = agent_queues[0]
                print("Using agent queue:")
            except AzureDevOpsServiceError as e:
                print(f"Error fetching agent queues: {e}", file=sys.stderr)
                raise HTTPException(
                    status_code=500, detail=f"Failed to fetch agent queues: {str(e)}"
                )
            except ValueError:  # Catch "No agent queues found"
                raise  # Re-raise to be caught by outer handler

            build_repo = BuildRepository(
                id=repository_id,
                name=repository_name,
                type="TfsGit",
                default_branch=ref_default_branch,
            )
            # yaml_process = YamlProcess(yaml_filename=yaml_file_path)
            agent_pool_queue = AgentPoolQueue(id=agent_queue.id)

            ci_trigger_for_yaml_source = {
                "batchChanges": False,  # Default for CI trigger
                "branchFilters": [],  # Let YAML 'trigger:' define these; provide empty list
                "pathFilters": [],  # Let YAML 'trigger:' define these; provide empty list
                # CRUCIAL PART:
                "settingsSourceType": 2,  # 1 for UI, 2 for YAML file. This tells ADO the source.
                "triggerType": "continuousIntegration",  # From DefinitionTriggerType enum
                # Other optional properties for a CI trigger, often defaulted if not specified,
                # and likely superseded by YAML settings when settingsSourceType is 2:
                # "maxConcurrentBuildsPerBranch": 1,
                # "pollingInterval": 0, # Not for Azure Repos Git
                # "pipelineId": 0, # Not typically set here
            }

            pipeline_def = BuildDefinition(
                name=pipeline_name,
                repository=build_repo,
                process={"yamlFilename": yaml_file_path, "type": 2},
                queue=agent_pool_queue,
                project=TeamProjectReference(id=ado_project_guid),
                path=normalized_folder_path,
                triggers=[ci_trigger_for_yaml_source],
            )
            print(
                f"Submitting new pipeline definition for '{pipeline_name}' at path '{ui_path_display}'..."
            )
            try:
                created_pipeline = self._build_client.create_definition(
                    definition=pipeline_def, project=ado_project_guid
                )
                # print(
                #     f"Successfully created pipeline '{created_pipeline.name}' with ID: {created_pipeline.id}"
                # )
                if True:
                    print("Pipeline Web URL")
                print(
                    f"IMPORTANT: Ensure the YAML file '{yaml_file_path}' exists in repository '{repository_name}' on branch '{default_branch_name}'."
                )
                return created_pipeline
            except AzureDevOpsServiceError as e_create:
                error_detail_from_ado = getattr(e_create, "message", str(e_create))
                print(
                    f"Error creating pipeline definition '{pipeline_name}': {error_detail_from_ado}",
                    file=sys.stderr,
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to create pipeline definition '{pipeline_name}': {error_detail_from_ado}",
                )

        try:
            return await asyncio.to_thread(_create_pipeline_sync)
        except HTTPException:
            raise
        except ValueError as e:  # Catches "No agent queues found" from sync block
            print(
                f"Configuration error for pipeline '{pipeline_name}': {e}",
                file=sys.stderr,
            )
            raise HTTPException(
                status_code=400, detail=str(e)
            )  # Bad request due to config
        except Exception as e:
            print(
                f"Unexpected error in create_yaml_pipeline_definition for '{pipeline_name}': {e}",
                file=sys.stderr,
            )
            # import traceback; traceback.print_exc() # For detailed debugging
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected error creating pipeline '{pipeline_name}'. Details: {e}",
            )

    async def create_or_get_azure_repo(
        self, repo_name: str, seed_repo_details: Optional[Dict[str, str]] = None
    ) -> GitRepository:
        # ... (rest of your existing class code from here) ...
        ado_project_guid = await self._ensure_ado_project_guid()

        print(
            f"--- Ensuring Git Repository '{repo_name}' within ADO Project '{self.CONFIGURED_ADO_PROJECT_NAME}' (GUID: '{ado_project_guid}') ---"
        )
        print(f"Organization URL: {self.ORG_URL}")
        print(
            f"Using PAT: {'***' + self.PAT[-4:] if len(self.PAT) > 4 else 'Invalid PAT'}"
        )

        def _create_repo_logic_sync():
            try:
                existing_repo = self._git_client.get_repository(
                    repository_id=repo_name, project=ado_project_guid
                )
                print(
                    f"Git Repository '{repo_name}' already exists in project '{self.CONFIGURED_ADO_PROJECT_NAME}'. ID: {existing_repo.id}"
                )
                return existing_repo
            except AzureDevOpsServiceError as e:
                if "TF401019" in str(e) or "does not exist" in str(e).lower():
                    print(
                        f"Git Repository '{repo_name}' not found in project '{self.CONFIGURED_ADO_PROJECT_NAME}'. Proceeding to create."
                    )
                else:
                    print(
                        f"AzureDevOpsServiceError while checking for existing repo '{repo_name}': {e}",
                        file=sys.stderr,
                    )
                    raise

            print(
                f"Attempting to create new empty Git Repository: '{repo_name}' in ADO Project '{self.CONFIGURED_ADO_PROJECT_NAME}' (GUID '{ado_project_guid}')."
            )
            project_ref_for_ops = TeamProjectReference(id=ado_project_guid)
            repo_create_options = GitRepositoryCreateOptions(
                name=repo_name, project=project_ref_for_ops
            )

            try:
                new_repo = self._git_client.create_repository(
                    git_repository_to_create=repo_create_options,  # Corrected parameter name
                    project=ado_project_guid,
                )
                print(
                    f"Empty Git Repository '{new_repo.name}' created. ID: {new_repo.id}"
                )
            except AzureDevOpsServiceError as e_create:
                print(
                    f"Error creating Git Repository '{repo_name}': {e_create}",
                    file=sys.stderr,
                )
                raise

            if (
                seed_repo_details
                and seed_repo_details.get("url")
                and seed_repo_details.get("name")
            ):
                print(
                    f"Populating newly created Git Repository '{new_repo.name}' by importing from seed: {seed_repo_details['name']}"
                )
                import_git_source = GitImportGitSource(url=seed_repo_details["url"])
                import_parameters = GitImportRequestParameters(
                    git_source=import_git_source,
                    delete_service_endpoint_after_import_is_done=True,
                )
                import_request_descriptor = GitImportRequest(
                    parameters=import_parameters
                )
                try:
                    import_job = self._git_client.create_import_request(
                        import_request=import_request_descriptor,
                        project=ado_project_guid,
                        repository_id=new_repo.id,
                    )
                except AzureDevOpsServiceError as e_import_start:
                    print(
                        f"Error initiating import for repository '{new_repo.name}': {e_import_start}",
                        file=sys.stderr,
                    )
                    raise

                print(
                    f"Import request submitted for '{new_repo.name}'. Import ID: {import_job.import_request_id}, Status: {import_job.status}"
                )
                max_retries, retry_count = 30, 0
                while retry_count < max_retries:
                    time.sleep(5)
                    current_import_status = self._git_client.get_import_request(
                        project=ado_project_guid,
                        import_request_id=import_job.import_request_id,
                        repository_id=new_repo.id,
                    )
                    print(
                        f"  Import status for '{new_repo.name}': {current_import_status.status}"
                    )
                    if current_import_status.status == "completed":
                        print(
                            f"Git Repository '{new_repo.name}' (ID: {new_repo.id}) imported successfully!"
                        )
                        return self._git_client.get_repository(
                            repository_id=new_repo.id, project=ado_project_guid
                        )
                    elif current_import_status.status in ["failed", "abandoned"]:
                        error_msg = f"Error: Import for '{new_repo.name}' (ID: {new_repo.id}) {current_import_status.status}."
                        detailed_error = getattr(
                            current_import_status, "detailed_status", None
                        )
                        if detailed_error and getattr(
                            detailed_error, "error_message", None
                        ):
                            error_msg += f" Details: {detailed_error.error_message}"
                        print(error_msg, file=sys.stderr)
                        raise AzureDevOpsServiceError(error_msg)
                    retry_count += 1
                timeout_msg = f"Error: Import for '{new_repo.name}' (ID: {new_repo.id}) timed out after {max_retries * 5 / 60:.1f} minutes."
                print(timeout_msg, file=sys.stderr)
                raise AzureDevOpsServiceError(timeout_msg)
            return new_repo

        try:
            return await asyncio.to_thread(_create_repo_logic_sync)
        except AzureDevOpsServiceError as e:
            print(
                f"Error during create_or_get_azure_repo for Git Repository '{repo_name}': {e}",
                file=sys.stderr,
            )
            raise HTTPException(
                status_code=500,
                detail=f"Failed to create/get Git Repository '{repo_name}': {str(e)}",
            )
        except Exception as e:
            print(
                f"Unexpected error in create_or_get_azure_repo for Git Repository '{repo_name}': {e}",
                file=sys.stderr,
            )
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected error processing Git Repository '{repo_name}'.",
            )

    async def get_repository_by_name(self, repo_name: str) -> GitRepository:
        ado_project_guid = await self._ensure_ado_project_guid()
        print(
            f"Fetching Git Repository '{repo_name}' in ADO Project '{self.CONFIGURED_ADO_PROJECT_NAME}' (GUID '{ado_project_guid}')"
        )
        try:
            repo = await asyncio.to_thread(
                self._git_client.get_repository,
                repository_id=repo_name,
                project=ado_project_guid,
            )
            return repo
        except AzureDevOpsServiceError as e:
            if "TF401019" in str(e) or "does not exist" in str(e).lower():
                raise HTTPException(
                    status_code=404,
                    detail=f"Git Repository '{repo_name}' not found in ADO Project '{self.CONFIGURED_ADO_PROJECT_NAME}'. Error: {e}",
                )
            print(f"Error fetching Git Repository '{repo_name}': {e}", file=sys.stderr)
            raise HTTPException(
                status_code=500,
                detail=f"Error accessing Git Repository '{repo_name}': {e}",
            )
        except Exception as e:
            print(
                f"Unexpected error fetching Git Repository '{repo_name}': {e}",
                file=sys.stderr,
            )
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected error with Git Repository '{repo_name}'. Details: {e}",
            )

    async def _get_latest_commit_sha(self, repository_id: str, branch_name: str) -> str:
        ado_project_id_guid = await self._ensure_ado_project_guid()
        try:
            ref_name = f"refs/heads/{branch_name}"
            refs = await asyncio.to_thread(
                self._git_client.get_refs,
                repository_id=repository_id,
                project=ado_project_id_guid,
                filter=ref_name,
            )
            if not refs:
                print(
                    f"Branch '{branch_name}' not found in repo ID {repository_id}. Assuming new branch or initial commit."
                )
                return "0000000000000000000000000000000000000000"
            return refs[0].object_id
        except AzureDevOpsServiceError as e:
            print(
                f"Error fetching refs for branch '{branch_name}' in repo ID {repository_id}: {e}"
            )
            raise HTTPException(
                status_code=500,
                detail=f"Error accessing ADO branch '{branch_name}'. Details: {e}",
            )

    async def _get_repo_items_recursive(
        self, repository_id: str, path: str = "/"
    ) -> List[GitItem]:
        ado_project_id_guid = await self._ensure_ado_project_guid()
        try:
            items = await asyncio.to_thread(
                self._git_client.get_items,
                repository_id=repository_id,
                project=ado_project_id_guid,
                scope_path=path,
                recursion_level="full",
                include_content_metadata=True,
            )
            return items
        except AzureDevOpsServiceError as e:
            if "TF401019" in str(e) or "does not exist" in str(e).lower():
                print(
                    f"Path '{path}' likely empty or not found in repo ID {repository_id}. Returning empty list."
                )
                return []
            print(
                f"Error fetching items from repo ID {repository_id}, path '{path}': {e}"
            )
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching ADO repository content from '{path}'. Details: {e}",
            )

    async def _get_item_content(self, repository_id: str, item_path: str) -> bytes:
        ado_project_id_guid = await self._ensure_ado_project_guid()
        try:
            content_stream = await asyncio.to_thread(
                self._git_client.get_item_content,
                repository_id=repository_id,
                project=ado_project_id_guid,
                path=item_path,
            )
            content_bytes = b"".join(content_stream)
            return content_bytes
        except AzureDevOpsServiceError as e:
            print(
                f"AzureDevOpsServiceError fetching content for item '{item_path}' in repo ID {repository_id}: {e}"
            )
            raise HTTPException(
                status_code=500,
                detail=f"Error fetching ADO file content for '{item_path}'. Details: {e}",
            )
        except Exception as e:
            print(
                f"Unexpected error fetching content for item '{item_path}' in repo ID {repository_id}: {e}"
            )
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected error fetching ADO file content for '{item_path}'. Details: {e}",
            )

    async def _prepare_git_changes(
        self,
        init_mode: bool,
        template_repo_id: Optional[
            str
        ] = None,  # Make optional, only needed if init_mode is True
        target_remote_base_path: str = "/",  # Base path in the target repo where files will be placed
        target_repo_id: Optional[
            str
        ] = None,  # Repository ID for file existence checking
        is_initial_commit: bool = False,  # True if this is the first commit to an empty repo
    ) -> List[Change]:
        """
        Prepares a list of Git changes to be committed.

        If init_mode is True and template_repo_id is provided, it fetches all files
        from the template repository and prepares them as 'add' or 'edit' changes
        based on whether each file already exists in the target repository.

        If init_mode is False, it uses the files previously set by `set_files_to_commit()`
        and prepares them as 'add' or 'edit' changes.

        Args:
            init_mode: If True, pulls from template_repo_id. If False, uses self._commit_files.
            template_repo_id: The ID of the template repository (only used if init_mode is True).
            target_remote_base_path: The base path in the target repository where files will be placed.
            target_repo_id: The ID of the target repository for file existence checking.
            is_initial_commit: True if this is the first commit to an empty repo, forces all files to use "add".

        Returns:
            A list of Change objects ready for a Git commit.

        Raises:
            ValueError: If init_mode is True but template_repo_id is not provided.
        """
        git_changes: List[Change] = []
        processed_paths: set[str] = set()  # Keep track of paths to handle add vs. edit

        # Set repository context for file existence checking
        if target_repo_id:
            self._current_repo_id = target_repo_id

        if not target_remote_base_path or target_remote_base_path == "/":
            actual_base_path = ""  # Root of the repo
        else:
            actual_base_path = target_remote_base_path.strip("/")

        if init_mode:
            if not template_repo_id:
                raise ValueError(
                    "template_repo_id must be provided when init_mode is True."
                )

            print(f"Initializing from template repository ID: {template_repo_id}")
            print(
                f"Fetching content from template repo '{template_repo_id}' to be added under '{actual_base_path or '/'}'..."
            )

            try:
                # boilerplate_items will be List[azure.devops.v7_1.git.models.GitItem]
                template_items: List[GitItem] = await self._get_repo_items_recursive(
                    repository_id=template_repo_id,
                    path="/",  # Fetch all from template root
                )
            except Exception as e:
                print(
                    f"Error fetching items from template repository '{template_repo_id}': {e}"
                )
                # Depending on desired behavior, you might want to raise an exception here
                # or return an empty list of changes if the template is inaccessible.
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to fetch from template repo '{template_repo_id}': {e}",
                ) from e

            print(f"Found {len(template_items)} items in template repository.")

            for item in template_items:
                if item.git_object_type == "blob":  # Process only files
                    # item.path is relative to the template repo's root
                    relative_template_path = item.path.lstrip("/")

                    # Construct the target path in the new repository
                    if actual_base_path:
                        target_path = f"{actual_base_path}/{relative_template_path}"
                    else:
                        target_path = relative_template_path

                    target_path = target_path.replace("\\", "/")  # Normalize slashes

                    if (
                        target_path in processed_paths
                    ):  # Should not happen if template repo has unique paths
                        print(
                            f"Warning: Duplicate path '{target_path}' encountered from template. Skipping subsequent."
                        )
                        continue

                    file_content_bytes: bytes
                    try:
                        file_content_bytes = await self._get_item_content(
                            template_repo_id,
                            item.path,  # Fetch content using original item.path
                        )
                    except Exception as e:
                        print(
                            f"Warning: Could not get content for template file '{item.path}' from repo '{template_repo_id}', skipping. Error: {e}"
                        )
                        continue

                    content_for_ado: str
                    content_type_for_ado: str
                    try:
                        content_for_ado = file_content_bytes.decode("utf-8")
                        content_type_for_ado = "rawtext"
                    except UnicodeDecodeError:
                        print(
                            f"Info: Content of template file '{item.path}' is not valid UTF-8. Encoding as Base64."
                        )
                        content_for_ado = base64.b64encode(file_content_bytes).decode(
                            "ascii"
                        )
                        content_type_for_ado = "base64encoded"
                    except (
                        AttributeError
                    ):  # Should not happen if _get_item_content returns bytes
                        if isinstance(file_content_bytes, str):
                            content_for_ado = file_content_bytes
                            content_type_for_ado = "rawtext"
                        else:
                            print(
                                f"Error: _get_item_content for '{item.path}' returned unexpected type. Skipping."
                            )
                            continue

                    # Determine if file exists in target repo to choose between 'add' or 'edit'
                    # For initial commits, always use 'add' to avoid Azure DevOps errors
                    change_type = await self._determine_change_type(
                        target_path,
                        "main",
                        {"fileName": target_path, "content": content_for_ado},
                        is_initial_commit=is_initial_commit,
                    )

                    print(f"Template file '{target_path}' change type: {change_type}")

                    git_changes.append(
                        Change(
                            change_type=change_type,
                            item=GitItem(path=target_path),
                            new_content=ItemContent(
                                content=content_for_ado,
                                content_type=content_type_for_ado,
                            ),
                        )
                    )
                    processed_paths.add(target_path)
            print(f"Prepared {len(git_changes)} changes from template repository.")

        else:  # Not init_mode, so use self._commit_files
            if not self._commit_files:
                print(
                    "init_mode is False and no files were set via set_files_to_commit(). No changes to prepare."
                )
                return []

            print(
                f"Preparing changes from provided file list (self._commit_files) to be added/edited under '{actual_base_path or '/'}'..."
            )
            for file_to_commit in self._commit_files:
                relative_file_path = file_to_commit["fileName"].lstrip(
                    "/"
                )  # Ensure relative path
                content_from_component: str = file_to_commit["content"]

                if actual_base_path:
                    target_path = f"{actual_base_path}/{relative_file_path}"
                else:
                    target_path = relative_file_path

                target_path = target_path.replace("\\", "/")  # Normalize slashes

                # Determine if file exists to choose between 'add' or 'edit'
                # For initial commits, always use 'add' to avoid Azure DevOps errors
                change_type_str = await self._determine_change_type(
                    target_path,
                    "main",
                    file_to_commit,
                    is_initial_commit=is_initial_commit,
                )

                print(f"File '{target_path}' change type: {change_type_str}")

                if change_type_str in ["add", "edit"]:
                    # Remove any previous 'add' or 'edit' for the same path from this batch
                    git_changes = [
                        gc for gc in git_changes if gc.item.path != target_path
                    ]

                # For component content, assume 'rawtext'.
                # If content could be binary, a 'contentType' field in file_to_commit would be needed.
                # Or, attempt to decode and fallback to base64 like in the template section.
                # For simplicity here, assuming text content from self._commit_files.

                # Basic check for binary-like string (heuristic) - can be improved
                is_likely_binary_string = False
                try:
                    content_from_component.encode(
                        "utf-8"
                    )  # Check if it's valid utf-8 text
                except UnicodeEncodeError:  # If direct encoding fails, it might be non-utf8 text or already base64 encoded binary
                    try:
                        # Attempt to decode if it's base64, then re-encode to confirm it was binary
                        base64.b64decode(content_from_component, validate=True)
                        is_likely_binary_string = (
                            True  # If it decodes as base64, treat as if it was binary
                        )
                    except Exception:
                        # Not valid utf-8, not valid base64, treat as raw text but warn or error
                        print(
                            f"Warning: Content for '{target_path}' is not valid UTF-8. Treating as rawtext. Ensure it's correctly formatted."
                        )

                content_for_ado_commit: str
                content_type_for_ado_commit: str

                if is_likely_binary_string:  # Assuming if it was passed as string and base6anss, it was meant to be base64
                    content_for_ado_commit = (
                        content_from_component  # Assume it's already base64 encoded
                    )
                    content_type_for_ado_commit = "base64encoded"
                else:  # Assume raw text
                    content_for_ado_commit = content_from_component
                    content_type_for_ado_commit = "rawtext"

                git_changes.append(
                    Change(
                        change_type=change_type_str,
                        item=GitItem(path=target_path),
                        new_content=ItemContent(
                            content=content_for_ado_commit,
                            content_type=content_type_for_ado_commit,
                        ),
                    )
                )
                processed_paths.add(target_path)
            print(f"Prepared {len(git_changes)} changes from provided file list.")

        return git_changes

    async def _get_branch_latest_commit_id(
        self, repository_id: str, branch_name: str, project_id: str
    ) -> str:
        try:
            branch_name_for_api = branch_name.replace("refs/heads/", "")
            if not branch_name_for_api:
                raise ValueError(
                    f"Invalid branch name '{branch_name}' resulted in empty name for API call."
                )
            print(
                f"Attempting to fetch branch '{branch_name_for_api}' in repo '{repository_id}'..."
            )
            branch_stat = await asyncio.to_thread(
                self._git_client.get_branch,
                repository_id=repository_id,
                name=branch_name_for_api,
                project=project_id,
            )
            latest_commit_id = branch_stat.commit.commit_id
            print(
                f"Found branch '{branch_name_for_api}'. Latest commit ID: {latest_commit_id[:7]}"
            )
            return latest_commit_id
        except AzureDevOpsServiceError as e:
            error_str = str(e).lower()
            if (
                "not found" in error_str
                or "tf401179" in error_str
                or "vs403403" in error_str
            ):
                print(
                    f"Branch '{branch_name}' (API name: '{branch_name_for_api}') not found in repository '{repository_id}'. Returning initial commit SHA."
                )
                return "0000000000000000000000000000000000000000"
            else:
                print(
                    f"Error fetching branch '{branch_name}' (API name: '{branch_name_for_api}'): {e}"
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"Error fetching branch details for '{branch_name}': {str(e)}",
                )
        except Exception as e:
            print(
                f"Unexpected error fetching branch '{branch_name}' (API name: '{branch_name_for_api}'): {e}"
            )
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected error fetching branch details for '{branch_name}': {str(e)}",
            )

    async def _push_changes_to_repo(
        self,
        repository_id: str,
        branch_name: str,
        git_changes: List[Change],
        commit_message: str,
    ) -> str:
        ado_project_id_guid = await self._ensure_ado_project_guid()
        if not git_changes:
            print("No changes provided to push. Skipping.")
            old_object_id_for_push = await self._get_branch_latest_commit_id(
                repository_id, branch_name, ado_project_id_guid
            )
            if old_object_id_for_push == "0000000000000000000000000000000000000000":
                raise ValueError("No changes to push and branch does not exist.")
            return old_object_id_for_push

        if not branch_name.startswith("refs/heads/"):
            target_ref_name = f"refs/heads/{branch_name}"
        else:
            target_ref_name = branch_name
        branch_name_short = target_ref_name.replace("refs/heads/", "")

        for attempt in range(1, MAX_PUSH_ATTEMPTS + 1):
            print(
                f"Push attempt {attempt}/{MAX_PUSH_ATTEMPTS} to '{target_ref_name}'..."
            )
            old_object_id_for_push = await self._get_branch_latest_commit_id(
                repository_id, branch_name_short, ado_project_id_guid
            )
            if old_object_id_for_push == "0000000000000000000000000000000000000000":
                print(
                    f"Remote branch '{target_ref_name}' does not exist (or treated as new). This will be an initial commit."
                )
            else:
                print(
                    f"Current tip of remote branch '{target_ref_name}' is {old_object_id_for_push[:7]}"
                )

            ref_update = GitRefUpdate(
                name=target_ref_name, old_object_id=old_object_id_for_push
            )
            commit = GitCommitRef(comment=commit_message, changes=git_changes)
            push_operation = GitPush(ref_updates=[ref_update], commits=[commit])

            try:
                print(
                    f"Attempting to push {len(git_changes)} changes to '{target_ref_name}' (expecting remote at: {old_object_id_for_push[:7]})..."
                )
                pushed_result = await asyncio.to_thread(
                    self._git_client.create_push,
                    push=push_operation,
                    repository_id=repository_id,
                    project=ado_project_id_guid,
                )
                if (
                    pushed_result
                    and hasattr(pushed_result, "commits")
                    and pushed_result.commits
                ):
                    new_commit_id = pushed_result.commits[0].commit_id
                    print(
                        f"🚀 Push successful on attempt {attempt}. New commit ID: {new_commit_id}"
                    )
                    return new_commit_id
                else:
                    update_status_msg = (
                        "Unknown push failure or unexpected response structure."
                    )
                    if (
                        hasattr(pushed_result, "ref_updates")
                        and pushed_result.ref_updates
                        and not pushed_result.ref_updates[0].success
                    ):
                        update_status_msg = (
                            pushed_result.ref_updates[0].custom_messages
                            or "Ref update not successful."
                        )
                    print(
                        f"Push attempt {attempt} failed: {update_status_msg}. Push Result: {pushed_result}"
                    )
                    if attempt < MAX_PUSH_ATTEMPTS:
                        print(f"Retrying in {RETRY_DELAY_SECONDS} seconds...")
                        await asyncio.sleep(RETRY_DELAY_SECONDS)
                        continue
                    else:
                        raise HTTPException(
                            status_code=500,
                            detail=f"Azure DevOps push failed after {MAX_PUSH_ATTEMPTS} attempts: {update_status_msg}",
                        )
            except AzureDevOpsServiceError as e:
                error_message = str(e)
                if hasattr(e, "message") and e.message:
                    error_message = e.message
                print(f"Error during push attempt {attempt}: {error_message}")
                if "TF401028" in str(e) and attempt < MAX_PUSH_ATTEMPTS:
                    print(
                        f"TF401028 detected. Retrying in {RETRY_DELAY_SECONDS} seconds..."
                    )
                    await asyncio.sleep(RETRY_DELAY_SECONDS)
                elif "TF401027" in str(e) or (
                    "force push" in str(e).lower() and "not permitted" in str(e).lower()
                ):
                    detail_msg = f"Push rejected by server policy on branch '{branch_name_short}'. Error: {error_message}"
                    print(detail_msg)
                    raise HTTPException(status_code=403, detail=detail_msg)
                else:
                    raise HTTPException(
                        status_code=500,
                        detail=f"Azure DevOps push failed: {error_message}",
                    )
            except Exception as e:
                print(f"Unexpected error during push attempt {attempt}: {e}")
                raise HTTPException(
                    status_code=500, detail=f"Unexpected error during push: {str(e)}"
                )
        raise HTTPException(
            status_code=500,
            detail=f"Azure DevOps push failed after {MAX_PUSH_ATTEMPTS} attempts.",
        )

    async def find_or_create_ado_pipeline(
        self,
        pipeline_name: str,
        repo_id: str,
        repo_name: str,
        default_branch: str,  # Expects "main" or "refs/heads/main"
        yaml_path="azure-pipelines.yaml",
    ) -> Optional[BuildDefinition]:
        ado_project_guid = await self._ensure_ado_project_guid()
        print(
            f"--- Ensuring Azure Pipeline '{pipeline_name}' in ADO Project '{self.CONFIGURED_ADO_PROJECT_NAME}' ---"
        )

        # Normalize default_branch format
        if not default_branch.startswith("refs/heads/"):
            normalized_default_branch = f"refs/heads/{default_branch}"
        else:
            normalized_default_branch = default_branch

        try:
            # Using asyncio.to_thread for blocking SDK calls
            async def _find_or_create_pipeline_sync():
                pipelines = self._build_client.get_definitions(
                    project=ado_project_guid, name=pipeline_name
                )
                if pipelines:
                    print(
                        f"Pipeline '{pipeline_name}' already exists. ID: {pipelines[0].id}"
                    )
                    return pipelines[0]

                print(
                    f"Creating new pipeline '{pipeline_name}' for repo '{repo_name}'..."
                )
                # Use get_queues for v7.1 clients
                agent_queues = self._task_agent_client.get_agent_queues(
                    project=ado_project_guid, queue_name="Azure Pipelines"
                )
                if not agent_queues:
                    all_queues = self._build_client.get_q(project=ado_project_guid)
                    if not all_queues:
                        print(
                            f"Error: No agent queues found in ADO project '{self.CONFIGURED_ADO_PROJECT_NAME}'.",
                            file=sys.stderr,
                        )
                        # Raising an error might be better than returning None silently
                        raise ValueError(
                            f"No agent queues found in ADO project '{self.CONFIGURED_ADO_PROJECT_NAME}'."
                        )
                    agent_queue = all_queues[0]
                    print(
                        f"Warning: 'Azure Pipelines' queue not found. Using queue: {agent_queue.name}"
                    )
                else:
                    agent_queue = agent_queues[0]

                definition = BuildDefinition(
                    name=pipeline_name,
                    repository=BuildRepository(
                        id=repo_id,
                        name=repo_name,  # Repo name (e.g., "my-repo")
                        type="TfsGit",
                        default_branch=normalized_default_branch,  # Use normalized branch
                        properties={
                            "cleanOptions": "0"
                        },  # Example property: 0 for Sources
                    ),
                    # process=YamlProcess(yaml_filename=yaml_path), # Use YamlProcess model
                    queue=AgentPoolQueue(id=agent_queue.id),
                    project=TeamProjectReference(id=ado_project_guid),
                    path="\\",  # Default to root path
                )

                created_pipeline = self._build_client.create_definition(
                    definition=definition, project=ado_project_guid
                )
                print(
                    f"Pipeline '{created_pipeline.name}' created. ID: {created_pipeline.id}"
                )
                return created_pipeline

            return await asyncio.to_thread(_find_or_create_pipeline_sync)

        except ValueError as e:  # Catch "No agent queues found"
            print(
                f"Error finding or creating pipeline '{pipeline_name}': {e}",
                file=sys.stderr,
            )
            # Optionally re-raise as HTTPException or handle
            raise HTTPException(status_code=400, detail=str(e))
        except AzureDevOpsServiceError as e:
            print(
                f"AzureDevOpsServiceError finding or creating pipeline '{pipeline_name}': {e}",
                file=sys.stderr,
            )
            raise HTTPException(
                status_code=500, detail=f"Failed to find/create pipeline: {str(e)}"
            )
        except Exception as e:
            print(
                f"Unexpected error finding or creating pipeline '{pipeline_name}': {e}",
                file=sys.stderr,
            )
            # import traceback; traceback.print_exc()
            raise HTTPException(
                status_code=500, detail=f"Unexpected error with pipeline: {str(e)}"
            )

    async def set_pipeline_variable(
        self, pipeline_id: int, var_name: str, var_value: str, is_secret: bool = True
    ) -> bool:
        ado_project_guid = await self._ensure_ado_project_guid()
        print(
            f"Setting variable '{var_name}' for pipeline ID {pipeline_id} in ADO Project '{self.CONFIGURED_ADO_PROJECT_NAME}'..."
        )
        try:

            def _set_variable_sync():
                definition = self._build_client.get_definition(
                    project=ado_project_guid, definition_id=pipeline_id
                )
                if definition.variables is None:
                    definition.variables = {}
                definition.variables[var_name] = BuildDefinitionVariable(
                    value=var_value, is_secret=is_secret
                )
                updated_definition = self._build_client.update_definition(
                    definition=definition,
                    project=ado_project_guid,
                    definition_id=pipeline_id,
                )
                print(
                    f"Variable '{var_name}' set for pipeline '{updated_definition.name}'."
                )
                return True

            return await asyncio.to_thread(_set_variable_sync)
        except AzureDevOpsServiceError as e:
            print(
                f"AzureDevOpsServiceError setting variable '{var_name}' for pipeline ID {pipeline_id}: {e}",
                file=sys.stderr,
            )
            raise HTTPException(
                status_code=500, detail=f"Failed to set pipeline variable: {str(e)}"
            )
        except Exception as e:
            print(
                f"Error setting variable '{var_name}' for pipeline ID {pipeline_id}: {e}",
                file=sys.stderr,
            )
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected error setting pipeline variable: {str(e)}",
            )

    async def queue_build(
        self, pipeline_id: int, branch: str = "main"
    ) -> Optional[dict]:
        """
        Manually trigger a pipeline build.

        Args:
            pipeline_id: ID of the pipeline to trigger
            branch: Branch to build (default: main)

        Returns:
            Build information if successful, None otherwise
        """
        try:
            ado_project_guid = await self._ensure_ado_project_guid()

            # Normalize branch name
            if not branch.startswith("refs/heads/"):
                branch = f"refs/heads/{branch}"

            build_request = {"definition": {"id": pipeline_id}, "sourceBranch": branch}

            print(f"Triggering build for pipeline ID {pipeline_id} on branch {branch}")

            build = await asyncio.to_thread(
                self._build_client.queue_build,
                build=build_request,
                project=ado_project_guid,
            )

            print(
                f"Build queued successfully. Build ID: {build.id}, Status: {build.status}"
            )

            return {
                "id": build.id,
                "status": build.status,
                "queue_time": build.queue_time,
                "url": f"https://dev.azure.com/{self.ORG_URL.split('/')[-1]}/{self.CONFIGURED_ADO_PROJECT_NAME}/_build/results?buildId={build.id}",
            }

        except Exception as e:
            print(f"Error queuing build for pipeline {pipeline_id}: {e}")
            return None

    def generate_pipeline_yaml_content(
        self, build_output_dir="dist", default_branch="main"
    ):
        return f"""
trigger:
- {default_branch}

pool:
  vmImage: 'ubuntu-latest'

steps:
- task: NodeTool@0
  inputs:
    versionSpec: '18.x' # Or your preferred Node.js version
  displayName: 'Install Node.js'

- script: |
    npm install
    npm run build # Or your build command
  displayName: 'npm install and build'

- task: ArchiveFiles@2
  inputs:
    rootFolderOrFile: '{build_output_dir}'
    includeRootFolder: false
    archiveType: 'zip'
    archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
    replaceExistingArchive: true
  displayName: 'Archive build output'

- task: PublishBuildArtifacts@1
  inputs:
    PathtoPublish: '$(Build.ArtifactStagingDirectory)'
    ArtifactName: 'drop'
    publishLocation: 'Container'
  displayName: 'Publish Artifact: drop'
"""

    @staticmethod
    def commit_and_push_file(
        repo_clone_url_with_pat,
        local_clone_path,
        filename,
        file_content,
        commit_message,
        branch="main",
    ):
        if os.path.exists(local_clone_path):
            print(f"Cleaning up existing clone path: {local_clone_path}")
            shutil.rmtree(local_clone_path)
        os.makedirs(local_clone_path, exist_ok=True)

        print(f"Cloning repo to {local_clone_path}...")
        try:
            subprocess.run(
                ["git", "clone", repo_clone_url_with_pat, "."],
                cwd=local_clone_path,
                check=True,
                capture_output=True,
            )
            print("Clone successful.")

            # Checkout branch or create if not exists
            try:
                subprocess.run(
                    ["git", "checkout", branch],
                    cwd=local_clone_path,
                    check=True,
                    capture_output=True,
                    stderr=subprocess.PIPE,
                )
                print(f"Switched to branch '{branch}'.")
            except subprocess.CalledProcessError as e_checkout:
                if (
                    "did not match any file(s) known to git"
                    in e_checkout.stderr.decode().lower()
                    or "is not a commit and a branch"
                    in e_checkout.stderr.decode().lower()
                ):
                    print(f"Branch '{branch}' not found. Creating new branch.")
                    subprocess.run(
                        ["git", "checkout", "-b", branch],
                        cwd=local_clone_path,
                        check=True,
                        capture_output=True,
                    )
                else:
                    raise  # Re-raise other checkout errors

            file_path = os.path.join(local_clone_path, filename)
            os.makedirs(
                os.path.dirname(file_path), exist_ok=True
            )  # Ensure directory exists
            with open(file_path, "w") as f:
                f.write(file_content)
            print(f"File '{filename}' written/updated locally.")

            subprocess.run(
                ["git", "add", filename],
                cwd=local_clone_path,
                check=True,
                capture_output=True,
            )
            print("File staged.")
            subprocess.run(
                ["git", "commit", "-m", commit_message],
                cwd=local_clone_path,
                check=True,
                capture_output=True,
            )
            print("Committed.")
            subprocess.run(
                ["git", "push", "origin", branch],
                cwd=local_clone_path,
                check=True,
                capture_output=True,
            )
            print(f"Pushed to branch '{branch}' successfully.")
            return True
        except subprocess.CalledProcessError as e:
            print(f"Git operation failed: {e}")
            print(f"Stdout: {e.stdout.decode() if e.stdout else 'N/A'}")
            print(f"Stderr: {e.stderr.decode() if e.stderr else 'N/A'}")
            return False
        except Exception as e:
            print(f"An unexpected error occurred during commit_and_push_file: {e}")
            return False
        finally:
            if os.path.exists(local_clone_path):
                print(f"Cleaning up clone path: {local_clone_path}")
                shutil.rmtree(local_clone_path)

    def get_project_id(self, project_name_or_id: str) -> str | None:
        print(f"ADO: Fetching Project ID for: {project_name_or_id}")
        try:
            # This is a synchronous call, run in thread for async context if called from async method
            project = self._core_client.get_project(project_id=project_name_or_id)
            print(f"ADO: Found Project ID: {project.id}")
            return project.id
        except AzureDevOpsServiceError as e:
            print(f"ADO Error (get_project_id): {e}")
            # Consider raising HTTPException if this is used in an API flow
        except Exception as e:
            print(f"ADO Unexpected Error (get_project_id): {e}")
        return None

    def get_repo_id(self, project_id: str, repo_name_or_id: str) -> str | None:
        print(f"ADO: Fetching Repo ID for: {repo_name_or_id} in project {project_id}")
        try:
            # This is a synchronous call
            repository = self._git_client.get_repository(
                repository_id=repo_name_or_id, project=project_id
            )
            print(f"ADO: Found Repo ID: {repository.id}")
            return repository.id
        except AzureDevOpsServiceError as e:
            print(f"ADO Error (get_repo_id): {e}")
        except Exception as e:
            print(f"ADO Unexpected Error (get_repo_id): {e}")
        return None

    def create_service_hook(
        self,
        project_id: str,
        repo_id: str,
        branch_name: str,
        netlify_hook_url: str,
    ) -> bool:
        print(
            f"ADO: Creating Service Hook for repo ID {repo_id} in project {project_id}..."
        )
        if not branch_name.startswith("refs/heads/"):
            ado_branch_filter = f"refs/heads/{branch_name}"
        else:
            ado_branch_filter = branch_name

        subscription_payload = ServiceHookSubscription(
            publisher_id="tfs",
            event_type="git.push",
            resource_version="1.0",
            consumer_id="webHooks",
            consumer_action_id="httpRequest",
            publisher_inputs={
                "projectId": project_id,
                "repository": repo_id,
                "branch": ado_branch_filter,
            },
            consumer_inputs={
                "url": netlify_hook_url,
                "resourceDetailsToSend": "all",
                "messagesToSend": "none",
                "detailedMessagesToSend": "none",
            },
        )
        try:
            # Synchronous call
            subscription = self._service_client.create_subscription(
                subscription=subscription_payload,
                # project=project_id # The SDK for v7.0/v7.1 create_subscription does not take project here
                # publisher_inputs handles project scoping
            )
            print(f"ADO: Service Hook created successfully: ID {subscription.id}")
            return True
        except AzureDevOpsServiceError as e:
            print(f"ADO Error (create_service_hook): {e}")
            if (
                "already exists" in str(e).lower()
                or "conflicting subscription" in str(e).lower()
            ):
                print("ADO: A service hook with similar settings might already exist.")
            # Consider raising or returning specific error info
        except Exception as e:
            print(f"ADO Unexpected Error (create_service_hook): {e}")
        return False

    # This create_pipeline method seems to be an older/alternative version.
    # The new `create_yaml_pipeline_definition` or the improved `find_or_create_ado_pipeline`
    # are generally preferred for v7.1 SDK usage.
    def create_pipeline(
        self, azure_repo_name, azure_project_name, trigger_branch="main"
    ):
        """
        Creates a new Azure DevOps pipeline definition from a YAML file.
        Note: Consider using `create_yaml_pipeline_definition` or `find_or_create_ado_pipeline` instead.
        """
        print(
            f"--- Attempting to create pipeline for repo '{azure_repo_name}' in project '{azure_project_name}' (Legacy Method) ---"
        )
        try:
            # These are synchronous calls, wrap in asyncio.to_thread if called from async
            project_details = self._core_client.get_project(
                project_id=azure_project_name
            )
            if not project_details:
                print(f"Error: Project '{azure_project_name}' not found.")
                return None

            repository = self._git_client.get_repository(
                repository_id=azure_repo_name,
                project=project_details.id,  # Use project ID
            )
            repo_id = repository.id
            print(f"Found repository ID: {repo_id} for repo '{azure_repo_name}'")

            pipeline_name = f"Pipeline-{azure_repo_name}"
            print(f"Preparing pipeline definition for '{pipeline_name}'...")

            # Normalize trigger_branch
            if not trigger_branch.startswith("refs/heads/"):
                normalized_trigger_branch = f"refs/heads/{trigger_branch}"
            else:
                normalized_trigger_branch = trigger_branch

            build_repo = BuildRepository(
                id=repo_id,
                name=azure_repo_name,
                type="TfsGit",
                default_branch=normalized_trigger_branch,
            )

            # Get Agent Queue
            agent_queues = self._task_agent_client.get_agent_queues(
                project=project_details.id, queue_name="Azure Pipelines"
            )
            if not agent_queues:
                all_queues = self._task_agent_client.get_agent_queues(
                    project=project_details.id
                )
                if not all_queues:
                    print(
                        f"Error: No agent queues found in project '{azure_project_name}'."
                    )
                    return None
                agent_queue_for_pipeline = all_queues[0]
                print(
                    f"Warning: 'Azure Pipelines' queue not found. Using first available: {agent_queue_for_pipeline.name}"
                )
            else:
                agent_queue_for_pipeline = agent_queues[0]

            agent_pool_queue_obj = AgentPoolQueue(id=agent_queue_for_pipeline.id)

            pipeline_definition = BuildDefinition(
                name=pipeline_name,
                repository=build_repo,
                process={
                    "yamlFilename": "azure-pipelines.yaml",
                    "type": 2,
                },  # Use YamlProcess object
                queue=agent_pool_queue_obj,
                project=TeamProjectReference(id=project_details.id),
                path="\\",  # Default path
            )

            print(
                f"Creating pipeline '{pipeline_name}' in project '{azure_project_name}'..."
            )
            created_pipeline = self._build_client.create_definition(
                definition=pipeline_definition,
                project=project_details.id,  # Use project ID
            )

            print("Successfully created pipeline!")
            print(f"  ID: {created_pipeline.id}")
            print(f"  Name: {created_pipeline.name}")
            if (
                created_pipeline._links
                and "web" in created_pipeline._links
                and "href" in created_pipeline._links["web"]
            ):
                print(f"  URL: {created_pipeline._links['web']['href']}")
            return created_pipeline

        except AzureDevOpsServiceError as e:
            print(f"Azure DevOps Service Error during create_pipeline (legacy): {e}")
            if (
                hasattr(e, "message")
                and hasattr(e, "response")
                and e.response is not None
            ):
                print(f"Error details from Azure DevOps: {e.message}")
                print(f"Response content: {e.response.text}")
        except Exception as e:
            print(f"An unexpected error occurred in create_pipeline (legacy): {e}")
            # import traceback; traceback.print_exc()
        return None

    # Add this method to your AzureRepoConnector class

    async def get_file_content(
        self,
        repository_name_or_id: str,
        file_path: str,
        project_name_or_id: Optional[
            str
        ] = None,  # Optional if using configured project
        branch_or_commit: Optional[
            str
        ] = None,  # Optional: defaults to the repo's default branch
        decode_as_text: bool = False,
        encoding: str = "utf-8",
    ) -> Union[bytes, str, None]:
        """
        Retrieves the raw content of a specific file from an Azure DevOps Git repository.

        Args:
            repository_name_or_id: The name or ID of the Git repository.
            file_path: The full path to the file within the repository (e.g., "src/app.js", "README.md").
            project_name_or_id: The name or ID of the project. If None, uses the configured project.
            branch_or_commit: The branch name, tag name, or commit SHA from which to get the file.
                              If None, the repository's default branch is used.
            decode_as_text: If True, attempts to decode the content as a string using the specified encoding.
                            If False (default), returns raw bytes.
            encoding: The encoding to use if decode_as_text is True (default: 'utf-8').

        Returns:
            The file content as bytes or a decoded string if decode_as_text is True.
            Returns None if the file is not found or an error occurs.
        """
        if project_name_or_id:
            target_project_id = await self.get_ado_project_details(project_name_or_id)
            project_guid = target_project_id.id
        else:
            project_guid = await self._ensure_ado_project_guid()

        print(
            f"Attempting to fetch content for file '{file_path}' in repository '{repository_name_or_id}' (Project GUID: {project_guid})"
        )
        if branch_or_commit:
            print(f"  Version: {branch_or_commit}")

        try:
            # The get_item_text and get_item_content methods actually handle blobs directly.
            # For specific versions, you use version_descriptor.
            version_descriptor = None
            if branch_or_commit:
                from azure.devops.v7_1.git.models import (
                    GitVersionDescriptor,
                    GitVersionType,
                )

                # Determine if it's a branch, commit, or tag (simplified check)
                # For a robust solution, you might need to check refs
                if len(branch_or_commit) == 40 and all(
                    c in "0123456789abcdefABCDEF" for c in branch_or_commit
                ):
                    version_type = GitVersionType.COMMIT
                # elif branch_or_commit.startswith('refs/tags/'):
                #     version_type = GitVersionType.TAG # More complex to just assume
                else:  # Assume branch otherwise
                    version_type = GitVersionType.BRANCH

                version_descriptor = GitVersionDescriptor(
                    version=branch_or_commit, version_type=version_type
                )
                # print(f"  Using GitVersionDescriptor: Version='{version_descriptor.version}', Type='{version_descriptor.version_type}'")

            # The get_item method can give metadata, including if it's a folder.
            # For actual content of a file (blob), get_item_content is better.
            # Using get_blob which is more direct for file content:

            # The get_item method with include_content=True can also work but is less direct
            # for just getting the raw bytes of a known file.
            # item_metadata = await asyncio.to_thread(
            #     self._git_client.get_item,
            #     repository_id=repository_name_or_id,
            #     project=project_guid,
            #     path=file_path,
            #     version_descriptor=version_descriptor,
            #     include_content=True # This would put content in item_metadata.content
            # )
            # if item_metadata.git_object_type == "tree": # It's a folder
            #     print(f"Error: Path '{file_path}' is a folder, not a file.")
            #     return None
            # raw_content_str = item_metadata.content # This is already a string if text

            # Using get_item_content as it returns a stream of bytes, good for binary or text
            content_stream = await asyncio.to_thread(
                self._git_client.get_item_content,  # Renamed from get_blob in later versions, get_item_content is current
                repository_id=repository_name_or_id,
                project=project_guid,
                path=file_path,
                version_descriptor=version_descriptor,
                # download=False # download=False gives an iterator; download=True tries to give a filename (not what we want here)
            )

            raw_bytes = b"".join(content_stream)  # Consume the iterator of byte chunks

            print(f"Successfully fetched {len(raw_bytes)} bytes for '{file_path}'.")

            if decode_as_text:
                try:
                    return raw_bytes.decode(encoding)
                except UnicodeDecodeError as ude:
                    print(
                        f"Warning: Could not decode file '{file_path}' using encoding '{encoding}'. Error: {ude}. Consider fetching as bytes or trying a different encoding."
                    )
                    # Optionally, you could return the raw_bytes here or raise the error
                    return None  # Or return raw_bytes to let caller handle
            else:
                return raw_bytes

        except AzureDevOpsServiceError as e:
            # More specific error checking for "file not found"
            # TF401019: The item $/project/file.txt could not be found in the repository RepoName at the version main.
            # VS403403: The revision descriptor GBmain could not be found. (If branch is wrong)
            # VS403404: The path {filePath} does not exist in the repository {repoName} at the specified version {version}.
            error_str = str(e).lower()
            if (
                "tf401019" in error_str
                or "vs403404" in error_str
                or "does not exist" in error_str
                or "could not be found" in error_str
            ):
                print(
                    f"Error: File '{file_path}' not found in repository '{repository_name_or_id}' (version: {branch_or_commit or 'default'}). Details: {e}"
                )
            elif "vs403403" in error_str:  # Incorrect branch/commit
                print(
                    f"Error: Version '{branch_or_commit}' not found for repository '{repository_name_or_id}'. Details: {e}"
                )
            else:
                print(f"AzureDevOpsServiceError fetching file '{file_path}': {e}")
            return None
        except Exception as e:
            print(f"Unexpected error fetching file '{file_path}': {e}")
            # import traceback; traceback.print_exc() # For detailed debugging
            return None

    async def update_pipeline_yaml_and_commit(
        self,
        repository_name_or_id: str,
        file_path: str,  # e.g., "azure-pipelines.yaml"
        current_yaml_content: str,
        new_app_name: str,
        target_branch: str,  # e.g., "main"
        commit_message: str,
        project_name_or_id: Optional[str] = None,
    ) -> Optional[str]:
        """
        Updates a specific placeholder in YAML content, then commits the change to Azure DevOps.
        Specifically targets 'SWA_APP_NAME' value 'your_app_name'.

        Args:
            repository_name_or_id: The name or ID of the Git repository.
            file_path: The full path to the YAML file within the repository to update.
            current_yaml_content: The current string content of the YAML file.
            new_app_name: The new application name to replace 'your_app_name'.
            target_branch: The branch to commit the changes to.
            commit_message: The commit message for this change.
            project_name_or_id: The name or ID of the project. If None, uses the configured project.

        Returns:
            The new commit SHA if successful, otherwise None.
        """
        if project_name_or_id:
            target_project_details = await self.get_ado_project_details(
                project_name_or_id
            )
            project_guid = target_project_details.id
        else:
            project_guid = await self._ensure_ado_project_guid()

        # Resolve repository ID if a name is given
        try:
            repo_details = await asyncio.to_thread(
                self._git_client.get_repository,
                repository_id=repository_name_or_id,
                project=project_guid,
            )
            repository_id = repo_details.id
            print(
                f"Operating on repository '{repo_details.name}' (ID: {repository_id})"
            )
        except AzureDevOpsServiceError as e:
            print(
                f"Error: Could not find repository '{repository_name_or_id}' in project '{project_guid}'. Details: {e}"
            )
            raise HTTPException(
                status_code=404,
                detail=f"Repository '{repository_name_or_id}' not found.",
            ) from e
        except Exception as e:
            print(f"Unexpected error getting repository details: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Error accessing repository '{repository_name_or_id}'.",
            ) from e

        print(
            f"Updating '{file_path}' in repository '{repository_id}' on branch '{target_branch}'."
        )
        print(f"  Replacing 'your_app_name' with '{new_app_name}' for SWA_APP_NAME.")

        placeholder_line = "- name: SWA_APP_NAME"
        value_line_to_replace = "  value: 'your_app_name'"  # Assuming single quotes
        new_value_line = f"  value: '{new_app_name}'"

        lines = current_yaml_content.splitlines()
        modified_lines = []
        replaced = False
        i = 0
        while i < len(lines):
            line = lines[i]
            if placeholder_line in line:
                # Check if the next line is the one we want to replace
                if (
                    i + 1 < len(lines)
                    and value_line_to_replace.strip() == lines[i + 1].strip()
                ):
                    modified_lines.append(line)  # Keep the "- name: SWA_APP_NAME" line
                    modified_lines.append(new_value_line)  # Add the new value line
                    replaced = True
                    i += 1  # Skip the old value line
                else:
                    modified_lines.append(line)
            else:
                modified_lines.append(line)
            i += 1

        if not replaced:
            print(
                f"Warning: Placeholder '{value_line_to_replace.strip()}' under '{placeholder_line}' not found in the YAML content. File will be committed as is or an error might occur if change is expected."
            )
            # Decide if you want to proceed if no replacement happened.
            # For this function's purpose, we probably expect a replacement.
            # If you want to commit even if no replacement, remove the following lines:
            # print("No changes made to the YAML content. Aborting commit.")
            # return None
            # If we proceed, the original content will be committed if it's considered an "edit"

        updated_yaml_content = "\n".join(modified_lines)
        if (
            updated_yaml_content == current_yaml_content and not replaced
        ):  # Double check if content actually changed
            print(
                "Content did not change after attempted replacement. No commit needed."
            )
            # You might want to return the latest commit SHA of the branch if no actual change
            # return await self._get_branch_latest_commit_id(repository_id, target_branch, project_guid)
            return None  # Or indicate no commit was made

        print("YAML content updated.")
        # print("New content preview:\n", updated_yaml_content[:300] + "...") # Optional: print preview

        # --- Prepare Git changes ---
        # We need to determine if this is an 'add' or 'edit'.
        # For simplicity, this function assumes the file exists and it's an 'edit'.
        # A more robust version would check if the file exists at file_path first.
        git_change = Change(
            change_type="edit",  # Assuming the file exists and we are editing it.
            # Use "add" if the file might not exist.
            item=GitItem(path=file_path),
            new_content=ItemContent(
                content=updated_yaml_content,
                content_type="rawtext",  # YAML is plain text
            ),
        )

        try:
            print(
                f"Attempting to commit changes to '{file_path}' on branch '{target_branch}'..."
            )
            new_commit_sha = await self._push_changes_to_repo(
                repository_id=repository_id,
                branch_name=target_branch,  # _push_changes_to_repo handles refs/heads/
                git_changes=[git_change],
                commit_message=commit_message,
            )
            if new_commit_sha:
                print(
                    f"Successfully committed changes. New commit SHA: {new_commit_sha}"
                )
            return new_commit_sha
        except HTTPException as e:
            # _push_changes_to_repo already raises HTTPException
            print(f"HTTPException during commit: {e.detail}")
            raise
        except Exception as e:
            print(f"Unexpected error during commit: {e}")
            # import traceback; traceback.print_exc()
            raise HTTPException(
                status_code=500, detail=f"Failed to commit YAML changes: {str(e)}"
            )

    async def _determine_change_type(
        self,
        file_path: str,
        branch_name: str = "main",
        file_info: dict = None,
        is_initial_commit: bool = False,
    ) -> str:
        """
        Determine whether a file should be added or edited based on its existence in the repository.

        Args:
            file_path: The path to the file in the repository
            branch_name: The branch to check for file existence
            file_info: Additional file information that may contain operation type hints
            is_initial_commit: True if this is the first commit to an empty repo

        Returns:
            "add" if file doesn't exist or is_initial_commit is True, "edit" if it exists
        """
        # For initial commits to empty repositories, always use "add" to avoid Azure DevOps errors
        if is_initial_commit:
            print(f"File '{file_path}' - using 'add' for initial commit")
            return "add"
        # Check if operation type is explicitly specified
        if file_info and "operationType" in file_info:
            operation_type = file_info["operationType"]
            if operation_type == "add":
                print(f"File '{file_path}' explicitly set to 'add'")
                return "add"
            elif operation_type == "edit":
                print(f"File '{file_path}' explicitly set to 'edit'")
                return "edit"
            # If "mixed", continue with auto-detection below

        try:
            # Use the repository context set during prepare_git_changes
            if hasattr(self, "_current_repo_id") and self._current_repo_id:
                repo_id = self._current_repo_id
            else:
                # Fallback: Use file extension heuristics for common cases
                print(
                    f"Warning: No repository context set for file existence check of '{file_path}'"
                )
                return self._guess_change_type_from_file_info(file_path)

            # Try to get the file content to see if it exists
            ado_project_id_guid = await self._ensure_ado_project_guid()

            try:
                await asyncio.to_thread(
                    self._git_client.get_item,
                    repository_id=repo_id,
                    project=ado_project_id_guid,
                    path=file_path,
                    include_content=False,  # We just want to check existence
                )
                # File exists, so we should edit it
                print(f"File '{file_path}' exists - using 'edit'")
                return "edit"

            except AzureDevOpsServiceError as e:
                # Check if it's a "not found" error
                error_str = str(e).lower()
                if (
                    "tf401019" in error_str
                    or "vs403404" in error_str
                    or "does not exist" in error_str
                    or "could not be found" in error_str
                ):
                    # File doesn't exist, so we should add it
                    print(f"File '{file_path}' does not exist - using 'add'")
                    return "add"
                else:
                    # Some other error, use fallback logic
                    print(
                        f"Error checking file existence for '{file_path}': {e}. Using fallback logic"
                    )
                    return self._guess_change_type_from_file_info(file_path)

        except Exception as e:
            print(
                f"Unexpected error determining change type for '{file_path}': {e}. Using fallback logic"
            )
            return self._guess_change_type_from_file_info(file_path)

    def _guess_change_type_from_file_info(self, file_path: str) -> str:
        """
        Fallback method to guess change type based on file characteristics.
        This helps when we can't check file existence in the repository.
        """
        # Common files that are usually edited (updated) in a project
        common_edit_files = [
            "package.json",
            "package-lock.json",
            "yarn.lock",
            "requirements.txt",
            "pyproject.toml",
            "Pipfile",
            "README.md",
            "README.rst",
            "CHANGELOG.md",
            "index.html",
            "index.js",
            "index.ts",
            "main.js",
            "main.ts",
            "main.py",
            "app.js",
            "app.ts",
            "app.py",
            "config.js",
            "config.json",
            "settings.py",
            ".gitignore",
            ".env.example",
        ]

        file_name = file_path.split("/")[-1].lower()

        # If it's a common file that gets updated, assume edit
        if file_name in common_edit_files:
            print(
                f"File '{file_path}' appears to be a common project file - using 'edit'"
            )
            return "edit"

        # For new component files, pages, or assets, assume add
        if any(
            pattern in file_path.lower()
            for pattern in [
                "/components/",
                "/pages/",
                "/assets/",
                "/public/",
                "/src/components/",
                "/src/pages/",
                "/src/assets/",
                "component.",
                "page.",
                ".component.",
                ".page.",
            ]
        ):
            print(
                f"File '{file_path}' appears to be a new component/page/asset - using 'add'"
            )
            return "add"

        # Default to add for safety
        print(f"File '{file_path}' - using default 'add'")
        return "add"

    async def _get_repository_id_async(
        self, project_name: str, repository_name: str
    ) -> str | None:
        """
        Asynchronously fetches the ID of a repository given its name and project.
        Caches the result to avoid repeated API calls.
        """
        # cache_key = (project_name, repository_name)
        # if cache_key in self._repo_id_cache:
        #     # print(f"Cache hit for repository ID: {project_name}/{repository_name}")
        #     return self._repo_id_cache[cache_key]

        loop = asyncio.get_running_loop()

        def _fetch_repo_id_sync():
            try:
                # print(f"Fetching repositories for project: {project_name}")
                repositories = self._git_client.get_repositories(project=project_name)
                for repo in repositories:
                    if repo.name.lower() == repository_name.lower():
                        # print(f"Found repository: {repo.name} with ID: {repo.id}")
                        return repo.id
                print(
                    f"Repository '{repository_name}' not found in project '{project_name}'."
                )
                return None
            except Exception as e:
                print(
                    f"Error fetching repository ID for '{repository_name}' in '{project_name}': {e}"
                )
                return None

        try:
            if hasattr(asyncio, "to_thread"):
                repo_id = await asyncio.to_thread(_fetch_repo_id_sync)
            else:
                repo_id = await loop.run_in_executor(None, _fetch_repo_id_sync)

            # if repo_id:
            #     self._repo_id_cache[cache_key] = repo_id
            return repo_id
        except Exception as e:
            print(f"An error occurred in _get_repository_id_async: {e}")
            return None

    import asyncio
    import traceback  # For printing stack trace in exception handling

    # This function assumes it's a method of a class where 'self' has:
    # - self._build_client: An instance of azure.devops.v7_1.build.build_client.BuildClient or similar.
    # - self._get_repository_id_async: An async method to fetch repository ID.

    async def get_build_status_by_commit(  # noqa: C901
        self,
        commit_sha,
        project_name,
        repository_name,
        pipeline_id=None,
        repository_type="TfsGit",
    ):
        """
        Asynchronously finds the latest build associated with a specific commit SHA and
        returns its full build object.

        Args:
            commit_sha (str): The full commit SHA you are searching for.
            project_name (str): The name of the Azure DevOps project.
            repository_name (str): The name of the repository.
            pipeline_id (int, optional): The ID of a specific pipeline to narrow down the search.
                                        If None, searches across all pipelines for the
                                        commit.
            repository_type (str, optional): The type of the repository. Defaults to "TfsGit" for Azure Repos.
                                            Other examples: "GitHub", "Bitbucket".

        Returns:
            The build object (azure.devops.v7_1.build.models.Build) if found,
            otherwise None.
        """

        if not commit_sha:
            print("Error: Commit SHA must be provided.")
            return None
        if not project_name:
            print("Error: Project name must be provided.")
            return None
        if not repository_name:
            print("Error: Repository name must be provided.")
            return None

        # Get repository ID asynchronously
        repo_id = await self._get_repository_id_async(project_name, repository_name)
        if not repo_id:
            print(
                f"Could not find repository ID for '{repository_name}' in project '{project_name}'. Cannot proceed."
            )
            return None

        # print(f"Using Repository ID: {repo_id} for repository name: {repository_name}")

        definitions_filter = [pipeline_id] if pipeline_id else None

        def _fetch_builds_sync():
            # This synchronous function encapsulates the blocking SDK call.
            # It will be run in a separate thread.

            # Strategy: Fetch recent builds and filter client-side by source_version.
            # The Azure DevOps Python SDK's get_builds method does not directly expose
            # a 'sourceVersion' parameter for server-side filtering by commit SHA.
            # Thus, we fetch a list of recent builds and iterate through them.
            # Parameters 'query_order', 'max_builds_per_definition', and 'top'
            # help narrow down the search space effectively.

            builds_list = self._build_client.get_builds(
                project=project_name,
                repository_id=repo_id,
                repository_type=repository_type,
                definitions=definitions_filter,
                query_order="finishTimeDescending",  # Crucial for finding the "latest" build
                max_builds_per_definition=5,  # Limits builds per pipeline if 'definitions' is not specific
                top=25,  # Overall limit on the number of builds to fetch
            )

            found_build = None
            if builds_list:
                for build in builds_list:
                    # The commit SHA is usually in build.source_version for Git-based repositories.
                    # Using startswith() allows matching even if 'commit_sha' is a short SHA,
                    # assuming build.source_version stores the full SHA.
                    if build.source_version and build.source_version.startswith(
                        commit_sha
                    ):
                        found_build = build
                        break  # Found the latest build for this commit due to 'finishTimeDescending' order

            # Return as a list (even with one item) for consistency with how it's handled.
            return [found_build] if found_build else []

        try:
            # Run the synchronous SDK call in a separate thread to avoid blocking asyncio event loop
            if hasattr(asyncio, "to_thread"):  # Preferred for Python 3.9+
                builds_result_list = await asyncio.to_thread(_fetch_builds_sync)
            else:  # Fallback for Python < 3.9
                loop = asyncio.get_running_loop()
                builds_result_list = await loop.run_in_executor(
                    None, _fetch_builds_sync
                )

            if not builds_result_list:  # Will be an empty list if no build was found
                print(
                    f"No build found for commit '{commit_sha[:12]}' in repository '{repository_name}'"
                    f"{f' for pipeline ID {pipeline_id}' if pipeline_id else ''} (project: {project_name})."
                )
                return None

            latest_build = builds_result_list[
                0
            ]  # We filtered to get the specific build

            # Verbose output for demonstration; consider using logging in a production environment.
            print(f"\n--- Build Details for Commit {commit_sha[:12]} (Async) ---")
            print(f"  Build ID: {latest_build.id}")
            print(
                f"  Pipeline: {latest_build.definition.name} (ID: {latest_build.definition.id})"
            )
            print(f"  Queued Time: {latest_build.queue_time}")
            print(f"  Start Time: {latest_build.start_time}")
            print(f"  Finish Time: {latest_build.finish_time}")
            print(f"  Status: {latest_build.status}")
            print(
                f"  Result: {latest_build.result if latest_build.result else 'N/A (not completed or no result)'}"
            )
            print(f"  Source Branch: {latest_build.source_branch}")
            print(f"  Source Version (Commit): {latest_build.source_version}")

            web_link = "N/A"
            if (
                latest_build._links
                and latest_build._links.links
                and "web" in latest_build._links.links
            ):
                web_link_info = latest_build._links.links.get("web")
                if isinstance(web_link_info, dict):
                    web_link = web_link_info.get("href", "N/A")
            print(f"  Build URL: {web_link}")
            print("-------------------------------------------------")

            return latest_build

        except Exception as e:
            print(f"An error occurred while fetching build status asynchronously: {e}")
            return None

    async def get_build_log_content(
        self, project_id: str, build_id: int, log_id: int
    ) -> str:
        """Fetches the content of a specific build log."""
        try:
            log_lines = await asyncio.to_thread(
                self._build_client.get_build_log_lines,
                project=project_id,
                build_id=build_id,
                log_id=log_id,
            )
            return "\n".join(log_lines)
        except Exception as e:
            print(f"Error fetching content for log ID {log_id}: {e}")
            return f"Error fetching log content: {e}"

    async def get_failed_task_logs_from_build(
        self, project_name: str, build_id: int
    ) -> Tuple[Optional[str], Optional[str]]:
        """
        Fetches logs for a specific build and extracts errors from the first failed task.
        Returns the name of the failed task and its log content.
        """
        ado_project_guid = await self._ensure_ado_project_guid()
        try:
            timeline = await asyncio.to_thread(
                self._build_client.get_build_timeline,
                project=ado_project_guid,
                build_id=build_id,
            )

            if not timeline or not timeline.records:
                print(f"No timeline records found for build ID {build_id}.")
                return None, None

            for record in timeline.records:
                # Find the first failed task with a log
                if record.result == "failed" and record.log:
                    failed_task_name = record.name or "Unknown Task"
                    print(
                        f"Found failed task: '{failed_task_name}'. Log ID: {record.log.id}"
                    )
                    log_content = await self.get_build_log_content(
                        project_id=ado_project_guid,
                        build_id=build_id,
                        log_id=record.log.id,
                    )

                    # Extract content between markers
                    start_marker = "> vite build"
                    end_marker = "---End of Oryx build logs---"

                    start_index = log_content.find(start_marker)
                    if start_index != -1:
                        # Search for the end marker after the start marker
                        end_index = log_content.find(end_marker, start_index)
                        if end_index != -1:
                            # Extract the content between the markers
                            extracted_content = log_content[
                                start_index + len(start_marker) : end_index
                            ]
                            return failed_task_name, extracted_content.strip()
                    return failed_task_name, log_content

            print("Could not find any failed task with logs in the timeline.")
            return None, None

        except Exception as e:
            print(
                f"Error fetching or parsing build timeline for build ID {build_id}: {e}"
            )
            import traceback

            traceback.print_exc()
            return None, None

    async def get_deployment_url_from_build(
        self, project_name: str, build_id: int
    ) -> Optional[str]:
        """
        Fetches logs for a successful build and extracts the deployment URL from the Static Web App deploy step.
        """
        ado_project_guid = await self._ensure_ado_project_guid()
        try:
            timeline = await asyncio.to_thread(
                self._build_client.get_build_timeline,
                project=ado_project_guid,
                build_id=build_id,
            )

            if not timeline or not timeline.records:
                print(f"No timeline records found for build ID {build_id}.")
                return None

            # Look for the deployment task. The name can vary slightly.
            deploy_task_name_substring = "Deploy to Azure Static Web App"
            for record in timeline.records:
                if (
                    record.name
                    and deploy_task_name_substring in record.name
                    and record.result == "succeeded"
                    and record.log
                ):
                    print(
                        f"Found successful deploy task: '{record.name}'. Log ID: {record.log.id}"
                    )
                    log_content = await self.get_build_log_content(
                        project_id=ado_project_guid,
                        build_id=build_id,
                        log_id=record.log.id,
                    )

                    # Parse the log content for the URL
                    match = re.search(
                        r"Visit your site at:\s*(https://[^\s]+)", log_content
                    )
                    if match:
                        raw_url = match.group(1)
                        # The log output can contain ANSI color codes, which might be captured.
                        # We clean the URL by removing any non-printable characters like '\u001b'.
                        cleaned_url = "".join(
                            char for char in raw_url if char.isprintable()
                        )
                        print(f"Extracted deployment URL: {cleaned_url}")
                        return cleaned_url
                    else:
                        print(
                            f"Could not find deployment URL in the log for task '{record.name}'."
                        )
                        # Continue checking other records just in case

            print(
                f"Could not find a successful '{deploy_task_name_substring}' task with a URL in the timeline."
            )
            return None

        except Exception as e:
            print(
                f"Error fetching or parsing build timeline for deployment URL. Build ID {build_id}: {e}"
            )
            import traceback

            traceback.print_exc()
            return None

    async def poll_build_status(
        self,
        commit_sha: str,
        project_name: str,
        repository_name: str,
        pipeline_id: Optional[int] = None,
        timeout_minutes: int = 15,
        poll_interval_seconds: int = 20,
    ) -> Dict[str, Any]:
        """
        Polls for the build status of a commit and retrieves logs on failure or URL on success.

        Args:
            commit_sha: The commit SHA to track.
            project_name: The Azure DevOps project name.
            repository_name: The repository name.
            pipeline_id: The specific pipeline ID to check.
            timeout_minutes: How long to wait for the build to complete.
            poll_interval_seconds: How often to check the status.

        Returns:
            A dictionary with the final build status, result, and other details.
            Example success: {'status': 'completed', 'result': 'succeeded', 'live_url': 'https://...', 'details': {...}}
            Example failure: {'status': 'completed', 'result': 'failed', 'failed_step': 'npm build', 'logs': '...', 'details': {...}}
        """
        start_time = time.time()
        timeout_seconds = timeout_minutes * 60

        print(
            f"Starting to poll for build status for commit {commit_sha[:7]} in repo {repository_name}. Timeout: {timeout_minutes} mins."
        )

        while time.time() - start_time < timeout_seconds:
            build = await self.get_build_status_by_commit(
                commit_sha=commit_sha,
                project_name=project_name,
                repository_name=repository_name,
                pipeline_id=pipeline_id,
            )

            if build:
                status = build.status
                result = build.result
                build_id = build.id

                if status == "completed":
                    print(f"Build {build_id} completed with result: {result}")

                    error_logs = None
                    failed_step = None
                    live_url = None

                    if result == "succeeded":
                        print("Build succeeded. Fetching deployment URL...")
                        live_url = await self.get_deployment_url_from_build(
                            project_name=project_name, build_id=build_id
                        )
                    elif result == "failed":
                        print("Build failed. Fetching logs...")
                        (
                            failed_step,
                            error_logs,
                        ) = await self.get_failed_task_logs_from_build(
                            project_name=project_name, build_id=build_id
                        )

                    return {
                        "status": status,
                        "result": result,
                        "logs": error_logs,
                        "failed_step": failed_step,
                        "live_url": live_url,
                        "build_id": build_id,
                        "details": build.as_dict(),
                    }

                elif status in ["cancelling", "postponed"]:
                    print(f"Build {build_id} has status '{status}'. Stopping polling.")
                    return {
                        "status": status,
                        "result": result,
                        "logs": None,
                        "failed_step": None,
                        "live_url": None,
                        "build_id": build_id,
                        "details": build.as_dict(),
                    }

                print(
                    f"Build status is '{status}'. Waiting for {poll_interval_seconds} seconds..."
                )
            else:
                print(
                    f"No build found yet for commit {commit_sha[:7]}. Waiting for {poll_interval_seconds} seconds..."
                )

            await asyncio.sleep(poll_interval_seconds)

        print(f"Polling timed out after {timeout_minutes} minutes.")
        return {
            "status": "timed_out",
            "result": None,
            "logs": None,
            "failed_step": None,
            "live_url": None,
            "build_id": None,
            "details": None,
        }


async def main():
    repo_connector = AzureRepoConnector()

    data = await repo_connector.get_build_status_by_commit(
        "de323e255c3475fca48c2417273706f05477ed53",
        "experience-studio-preview-projects",
        "salesflow-dashboard",
    )
    print(data)

    # # --- Example Usage of New Methods ---
    # new_repo_name_example = "my-brand-new-python-repo"
    # new_pipeline_name_example = f"{new_repo_name_example}-CI"
    # default_pipeline_branch = "main"
    # yaml_pipeline_file = "azure-pipelines.yaml"  # This file needs to exist in the repo

    # try:
    #     # 2. Create a new repository
    #     print(f"\n--- Testing create_new_repository for '{new_repo_name_example}' ---")
    #     # Set fail_if_exists=True if you want an error if it already exists
    #     created_repo = await repo_connector.create_new_repository(
    #         new_repo_name_example, fail_if_exists=False
    #     )

    #     if created_repo:
    #         print(
    #             f"Repository '{created_repo}' ensured. ID: {created_repo}, URL: {created_repo}"
    #         )

    #         print(
    #             f"\n--- Testing create_yaml_pipeline_definition for '{new_pipeline_name_example}' ---"
    #         )
    #         created_pipeline = await repo_connector.create_yaml_pipeline_definition(
    #             pipeline_name=new_pipeline_name_example,
    #             repository_id=created_repo.id,
    #             repository_name=created_repo.name,  # Pass just the repo name
    #             default_branch_name=default_pipeline_branch,
    #             yaml_file_path=yaml_pipeline_file,
    #             pipeline_folder_path="\\AutomatedPipelines",  # Example folder
    #             fail_if_exists=False,
    #         )
    #         if created_pipeline:
    #             print(
    #                 f"Pipeline '{created_pipeline.name}' ensured. ID: {created_pipeline.id}"
    #             )
    #             if created_pipeline._links and "web" in created_pipeline._links:
    #                 print(f"Pipeline URL: {created_pipeline._links['web']['href']}")

    # IMPORTANT: Before creating the pipeline, you would typically:
    # a. Clone the `created_repo.remote_url` locally.
    # b. Add an `azure-pipelines.yaml` (or your `yaml_pipeline_file`) to it.
    # c. Commit and push this YAML file to the `default_pipeline_branch`.
    #
    # The `commit_and_push_file` static method can be used for this,
    # or you can do it manually for testing.
    # For this example, we'll assume the YAML file will be pushed.
    #
    # Example of preparing the YAML file (if you want to automate this part):
    # yaml_content = repo_connector.generate_pipeline_yaml_content(default_branch=default_pipeline_branch)
    # pat_for_clone = repo_connector.PAT # Use the PAT for auth
    # # Construct clone URL with PAT: https://<PAT>@dev.azure.com/org/project/_git/repo
    # clone_url_with_pat = created_repo.remote_url.replace("https://", f"https://{pat_for_clone}@")
    # local_temp_clone_path = f"./temp_clone_{created_repo.name}"
    #
    # print(f"\nAttempting to commit '{yaml_pipeline_file}' to '{created_repo.name}'...")
    # success = AzureRepoConnector.commit_and_push_file( # Call as static method
    #     repo_clone_url_with_pat=clone_url_with_pat,
    #     local_clone_path=local_temp_clone_path,
    #     filename=yaml_pipeline_file,
    #     file_content=yaml_content,
    #     commit_message=f"Add initial {yaml_pipeline_file}",
    #     branch=default_pipeline_branch
    # )
    # if success:
    #    print(f"Successfully pushed '{yaml_pipeline_file}' to '{created_repo.name}'.")
    # else:
    #    print(f"Failed to push '{yaml_pipeline_file}'. Pipeline creation might fail or be invalid.")
    # except HTTPException as e:
    #     print(f"\n🛑 HTTP Error in test: {e.status_code} - {e.detail}")
    # except ValueError as e:
    #     print(f"\n🛑 Value Error in test: {e}")
    # except Exception as e:
    #     print(f"\n🛑 An unexpected error occurred in test: {e}")
    #     import traceback

    #     traceback.print_exc()


if __name__ == "__main__":
    # Ensure AzureADOConfig values are set, e.g., via environment variables
    # Example: export ADO_PAT="your_pat"
    #          export ADO_ORG="your_organization_name"
    #          export ADO_PROJECT_NAME="your_project_name"
    asyncio.run(main())
