import io
import zipfile
import asyncio
import time
from fastapi import HTTPException

from src.services.azure.ad_repo_connector import AzureRepoConnector
from src.services.database.db_operations import get_project, get_or_create_user, get_project_and_user_fast
from src.utils.logger import AppLogger

# Global connector instance for reuse
_connector = AzureRepoConnector()


async def create_project_zip(project_id: str, user_signature: str) -> bytes:
    """Create zip from project repo - FAST VERSION"""

    # Fast validation
    project, user_id = await get_project_and_user_fast(project_id, user_signature)

    if not project:
        raise HTTPException(404, "Project not found")
    if str(project["created_by"]) != str(user_id):
        raise HTTPException(403, "Access denied")

    # Generate repo name
    repo_name = f"{project['project_name'].lower().replace(' ', '-')}-{str(user_id)[-4:]}-{project_id[-4:]}"

    # Get repo and files
    repo = await _connector.create_or_get_azure_repo(repo_name)
    if not repo:
        raise HTTPException(404, "Repository not found")

    items = await _connector._get_repo_items_recursive(repo.id)
    files = [item for item in items if item.git_object_type == "blob"]

    if not files:
        raise HTTPException(404, "No files found")

    # Fetch all files concurrently
    async def fetch_file(item):
        try:
            content = await _connector.get_file_content(repo.id, item.path, decode_as_text=False)
            return item.path.lstrip("/"), content if isinstance(content, bytes) else content.encode('utf-8')
        except:
            return None

    results = await asyncio.gather(*[fetch_file(item) for item in files], return_exceptions=True)
    valid_files = [r for r in results if r and not isinstance(r, Exception)]

    if not valid_files:
        raise HTTPException(500, "Failed to fetch files")

    # Create zip (no compression for speed)
    buffer = io.BytesIO()
    with zipfile.ZipFile(buffer, 'w', zipfile.ZIP_STORED) as zf:
        for path, content in valid_files:
            zf.writestr(path, content)

    return buffer.getvalue()


async def create_project_zip_from_commit_fast(project_id: str, commit_id: str, user_signature: str) -> bytes:
    """Create zip from project repo at a specific commit - BLAZING FAST VERSION"""

    # Ultra-fast combined lookup - single DB connection
    project, user_id = await get_project_and_user_fast(project_id, user_signature)

    if not project:
        raise HTTPException(404, "Project not found")
    if str(project["created_by"]) != str(user_id):
        raise HTTPException(403, "Access denied")

    # Generate repo name (optimized)
    repo_name = f"{project['project_name'].lower().replace(' ', '-')}-{str(user_id)[-4:]}-{project_id[-4:]}"

    # Get repo and files at specific commit (no intermediate validation)
    repo = await _connector.create_or_get_azure_repo(repo_name)
    if not repo:
        raise HTTPException(404, "Repository not found")

    # Get items at commit (fail fast if commit doesn't exist)
    try:
        items = await _connector.get_repo_items_at_commit(repo.id, commit_id)
        files = [item for item in items if item.git_object_type == "blob"]
        if not files:
            raise HTTPException(404, "No files found at commit")
    except Exception:
        raise HTTPException(400, "Invalid commit or commit not found")

    # Fetch all files concurrently (optimized)
    async def fetch_file(item):
        try:
            content = await _connector.get_file_content(
                repo.id, item.path, branch_or_commit=commit_id, decode_as_text=False
            )
            return item.path.lstrip("/"), content if isinstance(content, bytes) else content.encode('utf-8')
        except:
            return None

    results = await asyncio.gather(*[fetch_file(item) for item in files], return_exceptions=True)
    valid_files = [r for r in results if r and not isinstance(r, Exception)]

    if not valid_files:
        raise HTTPException(500, "Failed to fetch files")

    # Create zip (optimized for speed)
    buffer = io.BytesIO()
    with zipfile.ZipFile(buffer, 'w', zipfile.ZIP_STORED) as zf:  # No compression for speed
        for path, content in valid_files:
            zf.writestr(path, content)

    return buffer.getvalue()
