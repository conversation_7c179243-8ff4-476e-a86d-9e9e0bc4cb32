import io
import zipfile
import asyncio
import time
from fastapi import HTTPException

from src.services.azure.ad_repo_connector import AzureRepoConnector
from src.services.database.db_operations import get_project, get_or_create_user
from src.utils.logger import AppLogger

# Global connector instance for reuse
_connector = AzureRepoConnector()
logger = AppLogger(__name__).get_logger()


async def create_project_zip(project_id: str, user_signature: str) -> bytes:
    """Create zip from project repo"""
    
    start_time = time.time()
    project_short_id = project_id[:8]
    
    logger.info(f"Starting download for project {project_short_id}")
    
    # Fast validation
    validation_start = time.time()
    project, user_id = await asyncio.gather(
        get_project(project_id),
        get_or_create_user(user_signature)
    )
    validation_time = time.time() - validation_start
    
    if not project:
        logger.error(f"Project {project_short_id} not found")
        raise HTTPException(404, "Project not found")
    if str(project["created_by"]) != str(user_id):
        logger.error(f"Access denied for project {project_short_id}")
        raise HTTPException(403, "Access denied")
    
    logger.info(f"Validation completed in {validation_time:.2f}s for project {project_short_id}")
    
    # Generate repo name
    repo_name = f"{project['project_name'].lower().replace(' ', '-')}-{str(user_id)[-4:]}-{project_id[-4:]}"
    
    # Get repo and files
    repo_start = time.time()
    repo = await _connector.create_or_get_azure_repo(repo_name)
    if not repo:
        logger.error(f"Repository {repo_name} not found")
        raise HTTPException(404, "Repository not found")
    
    items = await _connector._get_repo_items_recursive(repo.id)
    files = [item for item in items if item.git_object_type == "blob"]
    repo_time = time.time() - repo_start
    
    if not files:
        logger.error(f"No files found in repository {repo_name}")
        raise HTTPException(404, "No files found")
    
    logger.info(f"Found {len(files)} files in {repo_time:.2f}s for project {project_short_id}")
    
    # Fetch all files concurrently
    fetch_start = time.time()
    async def fetch_file(item):
        try:
            content = await _connector.get_file_content(repo.id, item.path, decode_as_text=False)
            return item.path.lstrip("/"), content if isinstance(content, bytes) else content.encode('utf-8')
        except:
            return None
    
    results = await asyncio.gather(*[fetch_file(item) for item in files], return_exceptions=True)
    valid_files = [r for r in results if r and not isinstance(r, Exception)]
    fetch_time = time.time() - fetch_start
    
    if not valid_files:
        logger.error(f"Failed to fetch any files for project {project_short_id}")
        raise HTTPException(500, "Failed to fetch files")
    
    failed_files = len(files) - len(valid_files)
    if failed_files > 0:
        logger.warning(f"Failed to fetch {failed_files} files for project {project_short_id}")
    
    logger.info(f"Fetched {len(valid_files)}/{len(files)} files in {fetch_time:.2f}s for project {project_short_id}")
    
    # Create zip
    zip_start = time.time()
    buffer = io.BytesIO()
    with zipfile.ZipFile(buffer, 'w', zipfile.ZIP_DEFLATED, compresslevel=1) as zf:
        for path, content in valid_files:
            zf.writestr(path, content)
    
    zip_data = buffer.getvalue()
    zip_time = time.time() - zip_start
    zip_size_mb = len(zip_data) / (1024 * 1024)
    
    total_time = time.time() - start_time
    
    logger.info(f"Created zip with {len(valid_files)} files ({zip_size_mb:.2f}MB) in {zip_time:.2f}s for project {project_short_id}")
    logger.info(f"Download ready for project {project_short_id} - Total time: {total_time:.2f}s")
    
    return zip_data


async def create_project_zip_from_commit(project_id: str, commit_id: str, user_signature: str) -> bytes:
    """Create zip from project repo at a specific commit"""

    start_time = time.time()
    project_short_id = project_id[:8]
    commit_short_id = commit_id[:7]

    logger.info(f"Starting download for project {project_short_id} at commit {commit_short_id}")

    # Fast validation
    validation_start = time.time()
    project, user_id = await asyncio.gather(
        get_project(project_id),
        get_or_create_user(user_signature)
    )
    validation_time = time.time() - validation_start

    if not project:
        logger.error(f"Project {project_short_id} not found")
        raise HTTPException(404, "Project not found")
    if str(project["created_by"]) != str(user_id):
        logger.error(f"Access denied for project {project_short_id}")
        raise HTTPException(403, "Access denied")

    logger.info(f"Validation completed in {validation_time:.2f}s for project {project_short_id}")

    # Generate repo name
    repo_name = f"{project['project_name'].lower().replace(' ', '-')}-{str(user_id)[-4:]}-{project_id[-4:]}"

    # Get repo and files at specific commit
    repo_start = time.time()
    repo = await _connector.create_or_get_azure_repo(repo_name)
    if not repo:
        logger.error(f"Repository {repo_name} not found")
        raise HTTPException(404, "Repository not found")

    # Validate commit exists by trying to get items at that commit
    try:
        items = await _connector.get_repo_items_at_commit(repo.id, commit_id)
        files = [item for item in items if item.git_object_type == "blob"]
        repo_time = time.time() - repo_start

        if not files:
            logger.error(f"No files found in repository {repo_name} at commit {commit_short_id}")
            raise HTTPException(404, f"No files found at commit {commit_short_id}")

        logger.info(f"Found {len(files)} files at commit {commit_short_id} in {repo_time:.2f}s for project {project_short_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error accessing commit {commit_short_id} in repository {repo_name}: {str(e)}")
        raise HTTPException(400, f"Invalid commit ID or commit not found: {commit_short_id}")

    # Fetch all files concurrently at the specific commit
    fetch_start = time.time()
    async def fetch_file_at_commit(item):
        try:
            content = await _connector.get_file_content(
                repo.id, item.path, branch_or_commit=commit_id, decode_as_text=False
            )
            return item.path.lstrip("/"), content if isinstance(content, bytes) else content.encode('utf-8')
        except:
            return None

    results = await asyncio.gather(*[fetch_file_at_commit(item) for item in files], return_exceptions=True)
    valid_files = [r for r in results if r and not isinstance(r, Exception)]
    fetch_time = time.time() - fetch_start

    if not valid_files:
        logger.error(f"Failed to fetch any files for project {project_short_id} at commit {commit_short_id}")
        raise HTTPException(500, "Failed to fetch files")

    failed_files = len(files) - len(valid_files)
    if failed_files > 0:
        logger.warning(f"Failed to fetch {failed_files} files for project {project_short_id} at commit {commit_short_id}")

    logger.info(f"Fetched {len(valid_files)}/{len(files)} files in {fetch_time:.2f}s for project {project_short_id} at commit {commit_short_id}")

    # Create zip
    zip_start = time.time()
    buffer = io.BytesIO()
    with zipfile.ZipFile(buffer, 'w', zipfile.ZIP_DEFLATED, compresslevel=1) as zf:
        for path, content in valid_files:
            zf.writestr(path, content)

    zip_data = buffer.getvalue()
    zip_time = time.time() - zip_start
    zip_size_mb = len(zip_data) / (1024 * 1024)

    total_time = time.time() - start_time

    logger.info(f"Created zip with {len(valid_files)} files ({zip_size_mb:.2f}MB) in {zip_time:.2f}s for project {project_short_id} at commit {commit_short_id}")
    logger.info(f"Download ready for project {project_short_id} at commit {commit_short_id} - Total time: {total_time:.2f}s")

    return zip_data
