# src/services/azure/ado_helper.py
import asyncio
from typing import Any, Dict, Optional
from fastapi import HTT<PERSON>Exception
from src.services.azure.ad_repo_connector import AzureRepoConnector
from src.settings.settings import AzureADOConfig


credential = AzureADOConfig()


class AdoHelper:
    def __init__(
        self,
        repo_name: str,
        yaml_filename: str,
        base_path: str = "\\ExperienceStudioPipelines",
        deploy_url_prefix: str = None,
    ):
        self.repo_connector = AzureRepoConnector()
        self.repo_name = repo_name
        self.yaml_filename = yaml_filename
        self.base_path = base_path
        self.deploy_url = (
            deploy_url_prefix
            if deploy_url_prefix is not None
            else self.repo_name.replace(" ", "_")
        )

    async def _update_swa_name_in_pipeline_yaml(
        self, repository_name: str, new_app_name: str
    ) -> Optional[str]:
        """
        Fetches the pipeline YAML file, replaces the placeholder Static Web App name,
        and commits the change back to the repository.

        Args:
            repository_name: The name of the repository to update.
            new_app_name: The new name for the Static Web App.

        Returns:
            The commit hash of the update if successful, otherwise None.
        """
        print(
            f"Attempting to update SWA name in '{self.yaml_filename}' to '{new_app_name}' for repo '{repository_name}'"
        )
        try:
            # 1. Fetch the current content of the YAML file
            current_yaml_content = await self.repo_connector.get_file_content(
                repository_name_or_id=repository_name,
                file_path=self.yaml_filename,
                decode_as_text=True,
            )

            if current_yaml_content is None:
                print(
                    f"Warning: Could not fetch '{self.yaml_filename}'. Skipping SWA name update."
                )
                return None

            # 2. Replace the placeholder string
            placeholder = "sample-swa-test-2"
            if placeholder not in current_yaml_content:
                print(
                    f"Warning: Placeholder '{placeholder}' not found in '{self.yaml_filename}'. The SWA name may have already been updated. Skipping commit."
                )
                return None  # No change needed

            updated_yaml_content = current_yaml_content.replace(
                placeholder, new_app_name
            )
            print(f"Placeholder '{placeholder}' replaced with '{new_app_name}'.")

            # 3. Commit the updated file
            commit_hash = await self.commit_multiple_files(
                repo_name=repository_name,
                files_to_commit=[
                    {"fileName": self.yaml_filename, "content": updated_yaml_content}
                ],
                commit_message=f"Configure Static Web App name to {new_app_name}",
                branch="main",
                operation_type="edit",  # Force edit since we know the file exists
            )

            if commit_hash:
                print(
                    f"Successfully committed SWA name update. New commit: {commit_hash}"
                )
            return commit_hash

        except HTTPException as e:
            # If fetching the file or committing fails with an HTTP error, re-raise it
            print(f"HTTP error while updating SWA name: {e.detail}")
            raise
        except Exception as e:
            print(f"An unexpected error occurred while updating SWA name: {e}")
            # Let the calling function handle the exception
            raise

    async def create_repository_and_deploy(
        self,
        repository_name: str,
        from_seed: bool,
        seed_repository_name: str,
        base_path_to_write: str = "/",
        swa_app_name: str = None,
    ):
        commit_hash = "-1"  # Default if no commit happens
        try:
            # 1. Get/Create repository objects
            print(
                f"Ensuring template Git Repository '{seed_repository_name}' exists..."
            )
            template_repo = await self.repo_connector.get_repository_by_name(
                seed_repository_name
            )

            print(
                f"Ensuring deployment Git Repository '{repository_name}' exists (or creating it)..."
            )
            deploy_repo = await self.repo_connector.create_or_get_azure_repo(
                repo_name=repository_name
                # seed_repo_details can be added if deploy_repo needs seeding
            )

            # First, commit the code from the template to the new repository
            initial_commit_hash = await self.commit_and_push(
                from_seed=from_seed,
                target_repo_id=deploy_repo.id,  # Push to the new repository
                base_path_to_write=base_path_to_write,
                template_repo_id=template_repo.id,  # Source template repository
                commit_message=f"Initial commit from template '{seed_repository_name}'",
            )
            print("Initial commit from seed hash: ", initial_commit_hash)

            # After seeding, update the pipeline YAML with the correct SWA name
            # SWA names must be globally unique and can only contain alphanumeric characters and hyphens.
            # swa_app_name = repository_name.lower().replace("_", "-").replace(" ", "-")
            update_commit_hash = await self._update_swa_name_in_pipeline_yaml(
                repository_name=deploy_repo.name, new_app_name=swa_app_name
            )

            # The final commit hash is the one from the update, if it happened. Otherwise, it's the initial one.
            commit_hash = (
                update_commit_hash if update_commit_hash else initial_commit_hash
            )
            print("Final commit hash for pipeline creation: ", commit_hash)
            # Then create the pipeline after the YAML file exists in the repository
            new_pipeline_name_example = f"CI-{self.repo_name}"
            default_pipeline_branch = "main"

            created_pipeline = (
                await self.repo_connector.create_yaml_pipeline_definition(
                    pipeline_name=new_pipeline_name_example,
                    repository_id=deploy_repo.id,
                    repository_name=deploy_repo.name,  # Pass just the repo name
                    default_branch_name=default_pipeline_branch,
                    yaml_file_path=self.yaml_filename,
                    pipeline_folder_path=self.base_path,  # Example folder
                    fail_if_exists=False,
                )
            )

            print("Created Pipeline: ", created_pipeline)

            # Trigger the pipeline manually to ensure deployment starts
            if created_pipeline and created_pipeline.id:
                print(
                    f"Triggering pipeline build for pipeline ID: {created_pipeline.id}"
                )
                build_info = await self.repo_connector.queue_build(
                    created_pipeline.id, "main"
                )
                if build_info:
                    print(
                        f"Pipeline triggered successfully. Build ID: {build_info['id']}"
                    )
                    print(f"Build URL: {build_info['url']}")
                else:
                    print("Warning: Failed to trigger pipeline build")

            return commit_hash

        except HTTPException:  # Re-raise HTTPExceptions directly
            raise
        except ValueError as e:  # Catch configuration errors like missing ADO_PROJECT
            print(f"❌ CONFIGURATION ERROR during Azure Repo SDK operation: {e}")
            raise HTTPException(
                status_code=400, detail=str(e)
            )  # Return as a client error
        except Exception as e:
            print(f"❌ UNEXPECTED ERROR during Azure Repo SDK operation: {e}")
            import traceback

            traceback.print_exc()
            raise HTTPException(
                status_code=500, detail=f"An unexpected error occurred: {str(e)}"
            )
        finally:
            print("--- SDK Operation Finished ---")

    def get_project_id(self, project_name: str) -> str:
        """Get the ID of an Azure DevOps project by name."""
        project = self.repo_connector.get_project_id(project_name)
        if not project:
            raise HTTPException(
                status_code=404, detail=f"Project '{project_name}' not found"
            )
        return project

    def get_repo_id(self, project_name: str, repository_name: str) -> str:
        """Get the ID of an Azure DevOps project by name."""
        project = self.repo_connector.get_repo_id(project_name, repository_name)
        if not project:
            raise HTTPException(
                status_code=404, detail=f"Project '{repository_name}' not found"
            )
        return project

    # YAML update method removed since seed project already contains correct azure-pipelines.yaml

    def set_files_to_commit(self, files_to_commit):
        """
        Set files to be committed. This method is now part of AdoHelper for better workflow integration.

        Args:
            files_to_commit: Can be a single dict with fileName/content or a list of such dicts
        """
        if isinstance(files_to_commit, dict):
            # Single file case - convert to list format expected by repo_connector
            files_to_commit = [files_to_commit]
        elif isinstance(files_to_commit, list):
            # Multiple files case - ensure each item has the right structure
            for file_item in files_to_commit:
                if (
                    not isinstance(file_item, dict)
                    or "fileName" not in file_item
                    or "content" not in file_item
                ):
                    raise ValueError(
                        "Each file must be a dict with 'fileName' and 'content' keys"
                    )
        else:
            raise ValueError("files_to_commit must be a dict or list of dicts")

        self.repo_connector.set_files_to_commit(files_to_commit)
        print(f"Set {len(files_to_commit)} file(s) to be committed")

    async def commit_multiple_files(
        self,
        repo_name: str,
        files_to_commit: list,
        commit_message: str = "Automated file commit",
        branch: str = "main",
        operation_type: str = "mixed",  # "add", "edit", or "mixed"
    ):
        """
        Commit multiple files to an existing Azure DevOps repository.
        This method is designed to replace GitHub's commit_multiple_files_async functionality.

        Args:
            repo_name: Name of the Azure DevOps repository
            files_to_commit: List of dicts with 'fileName' and 'content' keys
            commit_message: Commit message
            branch: Target branch name
            operation_type: Type of operation - "add" (new files), "edit" (existing files), or "mixed" (auto-detect)

        Returns:
            commit_hash: The SHA of the created commit
        """
        try:
            print(
                f"Committing {len(files_to_commit)} files to Azure DevOps repo: {repo_name}"
            )
            print(
                f"Files to commit: {[f.get('fileName', 'N/A') for f in files_to_commit[:5]]}{'...' if len(files_to_commit) > 5 else ''}"
            )

            # Get the repository
            repo = await self.repo_connector.get_repository_by_name(repo_name)
            if not repo:
                raise HTTPException(
                    status_code=404, detail=f"Repository '{repo_name}' not found"
                )

            print(f"Found repository: {repo.name} (ID: {repo.id})")

            # Add operation type context to files if not already present
            enhanced_files = []
            for file_info in files_to_commit:
                enhanced_file = file_info.copy()
                if "operationType" not in enhanced_file:
                    enhanced_file["operationType"] = operation_type
                enhanced_files.append(enhanced_file)

            # Commit the files
            commit_hash = await self.commit_and_push(
                from_seed=False,
                target_repo_id=repo.id,  # Push to the target repository
                base_path_to_write="/",
                files_to_commit=enhanced_files,
                commit_message=commit_message,
            )

            print(
                f"Successfully committed files to {repo_name}. Commit hash: {commit_hash}"
            )
            print(f"Repository URL: {repo.remote_url}")
            return commit_hash

        except Exception as e:
            print(f"Error committing files to Azure DevOps: {str(e)}")
            raise HTTPException(
                status_code=500, detail=f"Failed to commit files: {str(e)}"
            )

    async def commit_and_push(
        self,
        from_seed: bool,
        target_repo_id: str,  # This should be the target repository, not template
        base_path_to_write: str,
        files_to_commit=None,
        commit_message: str = None,
        template_repo_id: str = None,  # Add template_repo_id as separate parameter
    ):
        print("Preparing changes to be committed...")

        # If files_to_commit is provided, set them before preparing git changes
        if files_to_commit is not None:
            self.set_files_to_commit(files_to_commit)

        # Check if this is an initial commit (branch doesn't exist or is at zero commit)
        is_initial_commit = False
        try:
            latest_commit = await self.repo_connector._get_branch_latest_commit_id(
                target_repo_id,
                "main",
                await self.repo_connector._ensure_ado_project_guid(),
            )
            is_initial_commit = (
                latest_commit == "0000000000000000000000000000000000000000"
            )
            if is_initial_commit:
                print("Detected initial commit - all files will use 'add' operation")
        except Exception as e:
            # If we can't get the branch, assume it's an initial commit
            print(f"Could not get branch info (likely initial commit): {e}")
            is_initial_commit = True

        if from_seed:
            if not template_repo_id:
                raise ValueError("template_repo_id is required when from_seed=True")
            git_changes_to_push = await self.repo_connector._prepare_git_changes(
                from_seed,
                template_repo_id,
                "/",
                target_repo_id,  # Pass target repo ID for file existence checking
                is_initial_commit,  # Pass initial commit flag
            )
        else:
            git_changes_to_push = await self.repo_connector._prepare_git_changes(
                from_seed,
                None,
                "/",
                target_repo_id,  # Pass target repo ID for file existence checking
                is_initial_commit,  # Pass initial commit flag
            )

        if not git_changes_to_push:
            print(
                "No file changes to push. Operation considered complete without a new commit."
            )
            commit_hash = await self.repo_connector._get_latest_commit_sha(
                target_repo_id, "main"
            )
            print(
                f"--- Azure Repo SDK Operation Successful (no new changes) --- Current head: {commit_hash}"
            )
            return commit_hash

        # 3. Get the latest commit on the target branch of the deployment repo
        print("Fetching latest commit for branch 'main' in deployment repo...")

        print("Pushing changes to deployment repository...")

        # Use custom commit message if provided, otherwise use default
        default_message = "Initial commit" if from_seed else "Automated deployment"
        final_commit_message = commit_message if commit_message else default_message

        # FIXED: Push to target repository, not template repository
        commit_hash = await self.repo_connector._push_changes_to_repo(
            repository_id=target_repo_id,  # This was the bug - was using template_repo_id
            branch_name="main",
            git_changes=git_changes_to_push,
            commit_message=final_commit_message,
        )
        return commit_hash

    async def poll_build_status(
        self,
        commit_sha: str,
        repository_name: str,
        pipeline_id: Optional[int] = None,
        timeout_minutes: int = 15,
        poll_interval_seconds: int = 20,
    ) -> Dict[str, Any]:
        """
        Polls for the build status of a commit and retrieves logs on failure.
        This is a helper method that calls the underlying repo_connector's polling logic.
        """
        print(
            f"AdoHelper: Delegating polling for commit {commit_sha[:7]} to AzureRepoConnector."
        )
        # The repo_connector is already configured with the project name from settings
        return await self.repo_connector.poll_build_status(
            commit_sha=commit_sha,
            project_name=credential.ADO_PROJECT,
            repository_name=repository_name,
            pipeline_id=pipeline_id,
            timeout_minutes=timeout_minutes,
            poll_interval_seconds=poll_interval_seconds,
        )


async def main():
    ado_helper = AdoHelper()

    await ado_helper.create_repository(
        "EE_Auto_Pipeline_Setup_Test_10",
        True,
        "React_Tailwindcss_Template",
    )


if __name__ == "__main__":
    asyncio.run(main())
