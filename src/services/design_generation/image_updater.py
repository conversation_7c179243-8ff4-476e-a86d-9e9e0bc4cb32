import json
import logging
from bs4 import BeautifulSoup

# from openai import AzureOpenAI
from src.settings.settings import AzureDALLECreds

azure_dalle_creds = AzureDALLECreds()


class ImageUpdater:
    def __init__(self, html_content):
        self.soup = BeautifulSoup(html_content, "html.parser")
        self.img_gen_dict = {}

        try:
            # Initialize the AzureOpenAI client
            # self.client = AzureOpenAI(
            #     api_version="2024-05-01-preview",
            #     azure_endpoint=azure_dalle_creds.AZURE_DALLE_ENDPOINT,
            #     api_key=azure_dalle_creds.AZURE_DALLE_API_KEY,
            # )
            self.client = None
        except Exception:
            logging.error("Unable to initialize Azure Open AI", exc_info=True)

    def get_html_with_images(self):
        self.generate_images()
        return self.get_updated_html()

    def generate_images(self):
        # Find all image tags in the HTML
        img_tags = self.soup.find_all("img")

        # Make API calls to generate images based on alt_text
        for img in img_tags:
            alt_text = img.get("alt")
            if alt_text != "logo":
                try:
                    # Generate the image using the Azure OpenAI client
                    result = self.client.images.generate(
                        model="Dalle3", prompt=alt_text, n=1
                    )

                    # Parse the result to get the image URL
                    new_img_url = json.loads(result.model_dump_json())["data"][0]["url"]
                    self.img_gen_dict[alt_text] = new_img_url

                    # Update the src attribute with the new image URL
                    img["src"] = new_img_url

                except Exception as e:
                    # Log or print the error and continue
                    print(f"Failed to generate image for '{alt_text}': {e}")
                    continue

    def get_updated_html(self):
        return str(self.soup.prettify())

    def get_image(self, prompt: str):
        result = self.client.images.generate(model="Dalle3", prompt=prompt, n=1)
        return json.loads(result.model_dump_json())["data"][0]["url"]

    def save_to_file(self, filename):
        with open(filename, "w", encoding="utf-8") as file:
            file.write(self.get_updated_html())
        print("Images updated successfully.")
