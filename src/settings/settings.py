import os
from dotenv import load_dotenv

# Load environment variables from a .env file if present
load_dotenv()


class AzureSettings:
    def __init__(self):
        self.CLIENT_ID: str = os.getenv("AZURE_CLIENT_ID", "")
        self.CLIENT_SECRET: str = os.getenv("AZURE_CLIENT_SECRET", "")
        self.TENANT_ID: str = os.getenv("AZURE_TENANT_ID", "")
        self.AUTHORITY: str = f"https://login.microsoftonline.com/{self.TENANT_ID}"
        self.SCOPE: list = ["https://graph.microsoft.com/.default"]
        self.BLOB_STORAGE_URL: str = os.getenv("AZURE_STORAGE_SAS_URL", "")
        self.STORAGE_SAS_TOKEN: str = os.getenv("AZURE_STORAGE_SAS_TOKEN", "")
        self.AZURE_TEMPLATE_STORAGE_URL: str = os.getenv(
            "AZURE_TEMPLATE_STORAGE_URL", ""
        )
        self.AZURE_TEMPLATE_STORAGE_TOKEN: str = os.getenv(
            "AZURE_TEMPLATE_STORAGE_TOKEN", ""
        )

        self._validate()

    def _validate(self):
        if not self.CLIENT_ID:
            raise ValueError("AZURE_CLIENT_ID is not set")
        if not self.CLIENT_SECRET:
            raise ValueError("AZURE_CLIENT_SECRET is not set")
        if not self.TENANT_ID:
            raise ValueError("AZURE_TENANT_ID is not set")
        if not self.BLOB_STORAGE_URL:
            raise ValueError("AZURE_STORAGE_SAS_URL is not set")
        if not self.STORAGE_SAS_TOKEN:
            raise ValueError("AZURE_STORAGE_SAS_TOKEN is not set")
        if not self.AZURE_TEMPLATE_STORAGE_URL:
            raise ValueError("AZURE_TEMPLATE_STORAGE_URL is not set")
        if not self.AZURE_TEMPLATE_STORAGE_TOKEN:
            raise ValueError("AZURE_TEMPLATE_STORAGE_TOKEN is not set")


class AzureOpenAICreds:
    def __init__(self):
        self.AZURE_API_KEY: str = os.getenv("AZURE_API_KEY", "")
        self.AZURE_API_BASE: str = os.getenv("AZURE_API_BASE", "")
        self.AZURE_API_VERSION: str = os.getenv("AZURE_API_VERSION", "")
        self.AZURE_DEPLOYMENT_MODEL: str = os.getenv("AZURE_DEPLOYMENT_MODEL", "")
        self.AZURE_VISION_API_KEY: str = os.getenv("AZURE_VISION_API_KEY", "")
        self.AZURE_VISION_API_VERSION: str = os.getenv("AZURE_VISION_API_VERSION", "")
        self.AZURE_VISION_ENDPOINT: str = os.getenv("AZURE_VISION_ENDPOINT", "")
        self.AZURE_EMBEDDING_DEPLOYMENT_NAME: str = os.getenv(
            "AZURE_EMBEDDING_DEPLOYMENT_NAME", ""
        )

        self._validate()

    def _validate(self):
        if not self.AZURE_API_KEY:
            raise ValueError("AZURE_API_KEY is not set")
        if not self.AZURE_API_BASE:
            raise ValueError("AZURE_API_BASE is not set")
        if not self.AZURE_API_VERSION:
            raise ValueError("AZURE_API_VERSION is not set")
        if not self.AZURE_DEPLOYMENT_MODEL:
            raise ValueError("AZURE_DEPLOYMENT_MODEL is not set")
        if not self.AZURE_VISION_API_KEY:
            raise ValueError("AZURE_VISION_API_KEY is not set")
        if not self.AZURE_VISION_API_VERSION:
            raise ValueError("AZURE_VISION_API_VERSION is not set")
        if not self.AZURE_VISION_ENDPOINT:
            raise ValueError("AZURE_VISION_ENDPOINT is not set")
        if not self.AZURE_EMBEDDING_DEPLOYMENT_NAME:
            raise ValueError("AZURE_EMBEDDING_DEPLOYMENT_NAME is not set")


class AzureDALLECreds:
    def __init__(self):
        self.AZURE_DALLE_API_KEY: str = os.getenv("AZURE_DALLE_API_KEY", "")
        self.AZURE_DALLE_ENDPOINT: str = os.getenv("AZURE_DALLE_ENDPOINT", "")

        self._validate()

    def _validate(self):
        if not self.AZURE_DALLE_API_KEY:
            raise ValueError("AZURE_DALLE_API_KEY is not set")
        if not self.AZURE_DALLE_ENDPOINT:
            raise ValueError("AZURE_DALLE_ENDPOINT is not set")


class RedisConfig:
    def __init__(self):
        self.REDIS_URL: str = os.getenv("REDIS_URL", "")
        self.REDIS_STREAM_DEFAULT_MAXLEN: int = int(
            os.getenv("REDIS_STREAM_DEFAULT_MAXLEN", "")
        )
        self.REDIS_STREAM_DEFAULT_EXPIRE: int = int(
            os.getenv("REDIS_STREAM_DEFAULT_EXPIRE", "")
        )
        # Add timeout settings to fix connection issues
        self.REDIS_SOCKET_TIMEOUT: int = int(os.getenv("REDIS_SOCKET_TIMEOUT", "30"))
        self.REDIS_SOCKET_CONNECT_TIMEOUT: int = int(
            os.getenv("REDIS_SOCKET_CONNECT_TIMEOUT", "10")
        )
        self.REDIS_RETRY_ON_TIMEOUT: bool = (
            os.getenv("REDIS_RETRY_ON_TIMEOUT", "true").lower() == "true"
        )
        self._validate()

    def _validate(self):
        if not self.REDIS_URL:
            raise ValueError("REDIS_URL is not set")
        if not self.REDIS_STREAM_DEFAULT_EXPIRE:
            raise ValueError("REDIS_STREAM_DEFAULT_EXPIRE is not set")
        if not self.REDIS_STREAM_DEFAULT_MAXLEN:
            raise ValueError("REDIS_STREAM_DEFAULT_MAXLEN is not set")


class DACreds:
    def __init__(self):
        self.DA_USER_SIGNATURE: str = os.getenv("DA_USER_SIGNATURE", "")
        self.DA_BASE_URL: str = os.getenv("DA_PLATFORM_BASEURL", "")
        self.DA_API_ENDPOINT: str = os.getenv("DA_PLATFORM_ENDPOINT", "")
        self.DA_ACCESS_KEY: str = os.getenv("DA_API_KEY", "")
        self.DA_USECASE_IDENTIFIER: str = os.getenv("DA_USECASE_IDENTIFIER", "")
        self.DA_API_URL: str = f"{self.DA_BASE_URL}/{self.DA_API_ENDPOINT}"

        self._validate()

    def _validate(self):
        if not self.DA_USER_SIGNATURE:
            raise ValueError("DA_USER_SIGNATURE is not set")
        if not self.DA_BASE_URL:
            raise ValueError("DA_PLATFORM_BASEURL is not set")
        if not self.DA_API_ENDPOINT:
            raise ValueError("DA_PLATFORM_ENDPOINT is not set")
        if not self.DA_ACCESS_KEY:
            raise ValueError("DA_API_KEY is not set")
        if not self.DA_USECASE_IDENTIFIER:
            raise ValueError("DA_USECASE_IDENTIFIER is not set")


class DevConfig:
    def __init__(self):
        self.ENABLE_AUTH: str = os.getenv("ENABLE_AUTH", "false")
        self.API_RETRY_COUNTS: int = 3
        self.API_RETRY_WAIT_TIME: int = 2


class AzureADOConfig:
    def __init__(self):
        self.ADO_PAT: str = os.getenv("ADO_PAT", "")
        self.ADO_ORG: str = os.getenv("ADO_ORG", "")
        self.ADO_PROJECT: str = os.getenv("ADO_PROJECT", "")
        self.AZURE_RESOURCE_GROUP: str = os.getenv("AZURE_RESOURCE_GROUP", "")
        self.AZURE_SUBSCRIPTION_ID: str = os.getenv("AZURE_SUBSCRIPTION_ID", "")
        # AZURE_STATIC_WEB_APPS_API_TOKEN not needed - auto-generated in pipeline
        self._validate()

    def _validate(self):
        if not self.ADO_PAT:
            raise ValueError("ADO_PAT is not set")
        if not self.ADO_ORG:
            raise ValueError("ADO_ORG is not set")
        if not self.ADO_PROJECT:
            raise ValueError("ADO_PROJECT is not set")
        if not self.AZURE_RESOURCE_GROUP:
            raise ValueError("AZURE_RESOURCE_GROUP is not set")
        if not self.AZURE_SUBSCRIPTION_ID:
            raise ValueError("AZURE_SUBSCRIPTION_ID is not set")
        # AZURE_STATIC_WEB_APPS_API_TOKEN not needed - auto-generated in pipeline


class ChromaDBConfig:
    def __init__(self):
        self.CHROMA_HOST: str = os.getenv("CHROMA_HOST", "localhost")
        self.CHROMA_PORT: str = os.getenv("CHROMA_PORT", "8000")
        self.COLLECTION_NAME: str = os.getenv("COLLECTION_NAME", "")
        self._validate()

    def _validate(self):
        if not self.COLLECTION_NAME:
            raise ValueError("COLLECTION_NAME is not set")
        if not self.CHROMA_HOST:
            raise ValueError("CHROMA_HOST is not set")
        if not self.CHROMA_PORT:
            raise ValueError("CHROMA_PORT is not set")


class PixabayConfig:
    def __init__(self):
        self.PIXABAY_API_KEY: str = os.getenv("PIXABAY_API_KEY", "")
        self._validate()

    def _validate(self):
        if not self.PIXABAY_API_KEY:
            raise ValueError("PIXABAY_API_KEY is not set")


class Settings:
    def __init__(self):
        self.azure = AzureSettings()
        self.azure_openai_creds = AzureOpenAICreds()
        self.da_creds = DACreds()
        self.azure_dalle = AzureDALLECreds()
        self.devConfig = DevConfig()
        self.azure_ado_config = AzureADOConfig()
        self.ssh_public_key = os.getenv("SSH_PUBLIC_KEY_CONTENT", "")
        self.redis_config = RedisConfig()
        self.chromadb_config = ChromaDBConfig()
        self.pixabay_config = PixabayConfig()

    def _validate(self):
        if not self.ssh_public_key:
            raise ValueError("ssh_public_key is not set")


settings = Settings()
