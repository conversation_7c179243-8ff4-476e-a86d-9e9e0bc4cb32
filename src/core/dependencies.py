import redis.asyncio as redis
from typing import Optional

# This will hold the global connection pool instance.
# It is initialized to None.
redis_pool: Optional[redis.ConnectionPool] = None


def get_redis_pool() -> redis.ConnectionPool:
    """
    Dependency function to get the Redis pool.

    This function will be used in `Depends()` in your route files.
    It simply returns the global redis_pool object. If the pool hasn't been
    initialized yet (e.g., lifespan function hasn't run), this will
    rightfully cause an error, which is what we want.
    """
    if redis_pool is None:
        # This is a safeguard. In a correctly configured FastAPI app,
        # the lifespan event will run before any request, so this
        # should not be triggered.
        raise RuntimeError("Redis connection pool is not initialized.")
    return redis_pool
