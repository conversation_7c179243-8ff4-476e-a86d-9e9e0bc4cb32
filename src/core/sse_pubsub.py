# in a new file, e.g., utils/redis_stream.py or similar

import redis.asyncio as redis
import json
import logging
from typing import Optional, Dict, Any


class RedisStreamPublisher:
    """
    A class to handle publishing messages to Redis Streams.
    """

    def __init__(
        self,
        redis_client: redis.Redis,
        default_maxlen: int = 1000,
        default_expire_seconds: int = 3600,  # 1 hour
    ):
        """
        Initializes the RedisStreamPublisher.

        Args:
            redis_client: An initialized redis.asyncio.Redis client instance.
            default_maxlen: Default maximum number of entries in the stream (approximate).
            default_expire_seconds: Default expiration time for the stream in seconds.
                                   Set to 0 or None to not set an expiry by default.
        """
        # if not isinstance(redis_client, redis.Redis):
        #     # Ensure we get an async client, not the sync one by mistake
        #     if hasattr(redis_client, 'xadd') and not asyncio.iscoroutinefunction(redis_client.xadd):
        #             raise TypeError("redis_client must be an instance of redis.asyncio.Redis (async version).")
        #     elif not hasattr(redis_client, 'xadd'): # Basic check if it looks like a redis client
        #             raise TypeError("redis_client does not appear to be a valid Redis client instance.")

        self.redis_client = redis_client
        self.default_maxlen = default_maxlen
        self.default_expire_seconds = default_expire_seconds
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(
            f"RedisStreamPublisher initialized. Default MAXLEN=~{default_maxlen}, Default EXPIRE={default_expire_seconds}s"
        )

    def _generate_stream_name(self, stream_key_prefix: str, identifier: str) -> str:
        """Helper to consistently generate stream names."""
        return f"{stream_key_prefix}:{identifier}"

    async def publish_message(
        self,
        stream_key_prefix: str,
        identifier: str,
        message_data: Dict[str, Any],
        maxlen: Optional[int] = None,
        expire_seconds: Optional[int] = None,
        message_field_name: str = "data",
    ) -> Optional[str]:
        """
        Publishes a message to a Redis stream.

        Args:
            stream_key_prefix: The prefix for the stream key (e.g., "project_updates").
            identifier: A unique identifier to append to the prefix (e.g., request_id).
            message_data: A dictionary containing the message payload.
            maxlen: Maximum number of entries in the stream. Uses instance default if None.
            expire_seconds: Expiration time for the stream. Uses instance default if None.
                               Set to 0 to remove default expiry if one was set.
            message_field_name: The field name in the stream message to store the JSON payload.

        Returns:
            The ID of the message if successful, None otherwise.
        """
        stream_name = self._generate_stream_name(stream_key_prefix, identifier)

        # Use provided maxlen/expire_seconds or instance defaults
        current_maxlen = maxlen if maxlen is not None else self.default_maxlen
        current_expire_seconds = (
            expire_seconds
            if expire_seconds is not None
            else self.default_expire_seconds
        )

        try:
            # Redis stream messages are key-value pairs. We'll store the JSON string under 'data'.
            payload = {message_field_name: json.dumps(message_data)}

            message_id = await self.redis_client.xadd(
                name=stream_name,
                fields=payload,
                maxlen=current_maxlen,
                approximate=True,  # Needed for MAXLEN if it's not an exact power of 2
            )
            self.logger.debug(
                f"Published message ID {message_id} to stream {stream_name} with payload: {message_data}"
            )

            if current_expire_seconds and current_expire_seconds > 0:
                await self.redis_client.expire(stream_name, current_expire_seconds)
                self.logger.debug(
                    f"Set/updated expiration for stream {stream_name} to {current_expire_seconds} seconds."
                )

            return message_id
        except redis.TimeoutError as e:
            self.logger.warning(
                f"Redis timeout publishing to stream {stream_name}: {e}. Continuing without Redis update."
            )
        except redis.ConnectionError as e:
            self.logger.warning(
                f"Redis connection error publishing to stream {stream_name}: {e}. Continuing without Redis update."
            )
        except redis.RedisError as e:
            self.logger.error(
                f"Redis error publishing to stream {stream_name}: {e}", exc_info=True
            )
        except TypeError as e:  # Catch JSON serialization errors
            self.logger.error(
                f"Error serializing message data for stream {stream_name}: {e}",
                exc_info=True,
            )
        except Exception as e:
            self.logger.error(
                f"Unexpected error publishing to stream {stream_name}: {e}",
                exc_info=True,
            )
        return None

    async def publish_project_update(
        self,
        request_id: str,
        state_data: Dict[str, Any],
        maxlen: Optional[int] = None,
        expire_seconds: Optional[int] = None,
    ) -> Optional[str]:
        """
        Convenience method to publish project state updates.

        Args:
            request_id: The unique request ID for the project generation.
            state_data: The state dictionary to publish.
            maxlen: Override default maxlen for this specific message.
            expire_seconds: Override default expire_seconds for this specific message.

        Returns:
            The ID of the message if successful, None otherwise.
        """
        return await self.publish_message(
            stream_key_prefix="project_updates",  # Standardized prefix
            identifier=request_id,
            message_data=state_data,
            maxlen=maxlen,
            expire_seconds=expire_seconds,
        )
