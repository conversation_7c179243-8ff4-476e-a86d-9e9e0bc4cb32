from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
import redis.asyncio as redis_async


from src.routers import api_router
from src.model.response_schemas import DataResponse, HeartbeatResponse
from fastapi.middleware.cors import CORSMiddleware

from src.core.sse_pubsub import RedisStreamPublisher

from src.settings.settings import Settings

# from src.routers.code_generation import router

# --- Configuration ---
# Change this to "redis" to use Redis Streams
SSE_BACKEND = "redis"


settings = Settings().redis_config

# --- App Lifespan for managing connections ---


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Initialize Redis connection
    redis_client = redis_async.from_url(settings.REDIS_URL, decode_responses=True)
    app.state.redis_client = redis_client  # Keep if other parts need raw client

    # Initialize RedisStreamPublisher
    app.state.redis_stream_publisher = RedisStreamPublisher(
        redis_client=redis_client,
        default_maxlen=settings.REDIS_STREAM_DEFAULT_MAXLEN,  # e.g., 1000 from settings
        default_expire_seconds=settings.REDIS_STREAM_DEFAULT_EXPIRE,  # e.g., 7200 from settings
    )

    yield

    # Clean up Redis connection
    await app.state.redis_client.close()


app = FastAPI(lifespan=lifespan)

origins = [
    "http://localhost",
    "http://localhost:8000",
    "http://localhost:4201",
    "https://kind-island-0ff6ddd0f.6.azurestaticapps.net/",
    # Add more origins as needed
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=False,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router)
# app.include_router(code_generation_router)
app.mount("/static", StaticFiles(directory="src/static"), name="static")


@app.get("/")
async def root():
    return DataResponse(
        status="complete", data={"message": "Welcome to the FastAPI application!"}
    )


# Heartbeat endpoint
@app.get("/heartbeat", response_model=HeartbeatResponse)
async def heartbeat():
    return HeartbeatResponse(status="ok")
