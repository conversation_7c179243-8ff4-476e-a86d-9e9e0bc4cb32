# Project Commit IDs and Details

## Latest Project Information

### Project Details
- **Project ID**: `24367e97-153e-4d6c-9b79-d6e7bc0c863a`
- **Repository Name**: `insidebox-login-portal-d6a4-a3d4`
- **Repository ID**: `df50e11f-ac10-4ebd-9dfa-7119bda64bbb`
- **User Email**: `<EMAIL>`

### Commit History

#### 1. Initial Commit (Template Setup)
- **Commit ID**: `5933279a6b407a9b43746b7a3a7c44b3cdad0ff3`
- **Description**: Initial commit from React + Tailwind template
- **Files**: 87 template files added
- **Type**: Template initialization

#### 2. Pipeline Configuration Update
- **Commit ID**: `88d7995b84c116c8f8155a94666dc06189763ae6`
- **Description**: Updated azure-pipelines.yaml with SWA configuration
- **Files**: 1 file modified (azure-pipelines.yaml)
- **Type**: Configuration update

### Azure DevOps Details
- **Organization**: `ascendionava`
- **Project**: `experience-studio-preview-projects`
- **Project GUID**: `9e07f712-2d88-4eb4-981d-754c904f7f8b`
- **Pipeline ID**: `1457`
- **Pipeline Name**: `CI-insidebox-login-portal-d6a4-a3d4`

### Repository URLs
- **Git Repository**: `https://<EMAIL>/ascendionava/experience-studio-preview-projects/_git/insidebox-login-portal-d6a4-a3d4`
- **Pipeline URL**: `https://dev.azure.com/ascendionava/9e07f712-2d88-4eb4-981d-754c904f7f8b/_apis/build/Definitions/1457`

## Download Test URLs

### Test the Download Functionality

**1. Download from Initial Commit (Template):**
```
http://localhost:8000/download/commit/5933279a6b407a9b43746b7a3a7c44b3cdad0ff3?project_id=24367e97-153e-4d6c-9b79-d6e7bc0c863a&user_signature=<EMAIL>
```

**2. Download from Latest Commit (With Pipeline Config):**
```
http://localhost:8000/download/commit/88d7995b84c116c8f8155a94666dc06189763ae6?project_id=24367e97-153e-4d6c-9b79-d6e7bc0c863a&user_signature=<EMAIL>
```

### Debug URLs

**Check if project exists:**
```
http://localhost:8000/download/debug/project/24367e97-153e-4d6c-9b79-d6e7bc0c863a
```

**List all projects for user:**
```
http://localhost:8000/download/debug/projects?user_signature=<EMAIL>
```

### cURL Commands

**Download Latest Commit:**
```bash
curl -X GET "http://localhost:8000/download/commit/88d7995b84c116c8f8155a94666dc06189763ae6?project_id=24367e97-153e-4d6c-9b79-d6e7bc0c863a&user_signature=thanmai.sai%40ascendion.com" \
  -o project_latest.zip
```

**Download Initial Template:**
```bash
curl -X GET "http://localhost:8000/download/commit/5933279a6b407a9b43746b7a3a7c44b3cdad0ff3?project_id=24367e97-153e-4d6c-9b79-d6e7bc0c863a&user_signature=thanmai.sai%40ascendion.com" \
  -o project_template.zip
```

## Previous Project (For Reference)

### Old Project Details
- **Project ID**: `496966ae-ef2c-4a67-8e6e-a41e4182ef9b` ❌ (Not found in database)
- **Repository Name**: `insidebox-login-portal-d6a4-079b`
- **Repository ID**: `6d23eb02-cea0-4f0d-9822-2028f22574d6`
- **Commit ID**: `ec797a9aa98be448245cb319bcb93693bf59c569`

**Note**: The previous project was not properly saved to the database, which is why the download was failing.

## File Structure

The repository contains a complete React + Tailwind CSS application with:

- **Frontend Framework**: React with TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui component library
- **Build Tool**: Vite
- **Package Manager**: npm (with bun.lockb for faster installs)
- **Deployment**: Azure Static Web Apps
- **CI/CD**: Azure Pipelines

### Key Files
- `package.json` - Project dependencies
- `vite.config.ts` - Build configuration
- `tailwind.config.ts` - Tailwind CSS configuration
- `azure-pipelines.yaml` - CI/CD pipeline configuration
- `staticwebapp.config.json` - Azure SWA configuration
- `src/` - Source code directory
- `public/` - Static assets

## Next Steps

1. **Test the download** using the latest commit ID: `88d7995b84c116c8f8155a94666dc06189763ae6`
2. **Use the correct project ID**: `24367e97-153e-4d6c-9b79-d6e7bc0c863a`
3. **Verify the project exists** using the debug endpoints first

The download should work now with the correct project and commit IDs! 🚀
